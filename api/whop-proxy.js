/**
 * Whop Proxy Endpoint
 * This endpoint receives requests from the main app and forwards them to the intermediary server
 * while preserving the x-whop-user-token header that the <PERSON>p proxy adds
 */

// Use production URL for both development and production
// Note: In Vercel serverless functions, VITE_ prefixed variables aren't available
const INTERMEDIARY_SERVER_URL = process.env.WHOP_INTERMEDIARY_URL ||
                                process.env.VITE_WHOP_INTERMEDIARY_URL ||
                                'https://whop-intermediary-server.vercel.app';

export default async function handler(req, res) {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-whop-user-token');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    return res.status(200).end();
  }

  try {
    console.log('🔄 Whop Proxy: Forwarding request to intermediary server');
    console.log('📍 Method:', req.method);
    console.log('📍 URL:', req.url);
    console.log('📍 Headers:', Object.keys(req.headers));

    // Extract the endpoint from the query parameter
    const { endpoint } = req.query;

    if (!endpoint) {
      return res.status(400).json({
        success: false,
        error: 'Missing endpoint parameter'
      });
    }

    // Handle checkout-session endpoint directly
    if (endpoint.includes('whop/checkout-session')) {
      console.log('🛒 Handling checkout-session endpoint directly...');

      // Import the checkout-session handler dynamically
      try {
        const checkoutHandler = await import('./whop/checkout-session.js');
        return await checkoutHandler.default(req, res);
      } catch (importError) {
        console.error('❌ Failed to import checkout-session handler:', importError);
        // Continue with proxy logic as fallback
      }
    }

    // Construct the full URL to the intermediary server
    const targetUrl = `${INTERMEDIARY_SERVER_URL}/api/${endpoint}`;
    
    // Prepare headers to forward
    const forwardHeaders = {
      'Content-Type': 'application/json',
    };

    // Forward the Whop user token if present
    if (req.headers['x-whop-user-token']) {
      forwardHeaders['x-whop-user-token'] = req.headers['x-whop-user-token'];
      console.log('✅ Forwarding Whop user token to intermediary server');
      console.log('🔍 Token preview:', req.headers['x-whop-user-token'].substring(0, 20) + '...');
    } else {
      console.log('⚠️ No Whop user token found in request headers');
      console.log('📋 Available headers:', Object.keys(req.headers));
    }

    // Also forward other Whop-related headers that might be present
    const whopHeaders = [
      'x-whop-user-token',
      'x-whop-company-id',
      'x-whop-app-id',
      'x-whop-experience-id'
    ];

    whopHeaders.forEach(headerName => {
      if (req.headers[headerName]) {
        forwardHeaders[headerName] = req.headers[headerName];
        console.log(`✅ Forwarding ${headerName}`);
      }
    });

    // Forward other relevant headers
    if (req.headers['authorization']) {
      forwardHeaders['authorization'] = req.headers['authorization'];
    }

    // Prepare the request options
    const requestOptions = {
      method: req.method,
      headers: forwardHeaders,
    };

    // Add body for POST/PUT requests
    if (req.method === 'POST' || req.method === 'PUT') {
      requestOptions.body = JSON.stringify(req.body);
    }

    console.log('🚀 Forwarding to:', targetUrl);
    console.log('📤 Request options:', {
      method: requestOptions.method,
      headers: Object.keys(requestOptions.headers),
      hasBody: !!requestOptions.body
    });

    // Make the request to the intermediary server
    const response = await fetch(targetUrl, requestOptions);
    const data = await response.text();

    console.log('📥 Response from intermediary server:', {
      status: response.status,
      contentType: response.headers.get('content-type')
    });

    // Forward the response
    res.status(response.status);
    res.setHeader('Content-Type', response.headers.get('content-type') || 'application/json');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.end(data);

  } catch (error) {
    console.error('❌ Error in Whop proxy:', error);
    res.status(500).json({
      success: false,
      error: 'Proxy error',
      message: error.message
    });
  }
}
