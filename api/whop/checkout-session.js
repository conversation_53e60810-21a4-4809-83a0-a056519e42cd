import { WhopServerSdk } from '@whop/api';

// Initialize Whop SDK (server-side only)
const whopSdk = WhopServerSdk({
  appId: process.env.VITE_WHOP_APP_ID,
  appApiKey: process.env.WHOP_API_KEY,
  onBehalfOfUserId: process.env.VITE_WHOP_AGENT_USER_ID,
  companyId: process.env.VITE_WHOP_COMPANY_ID,
});

export default async (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-whop-user-token');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  try {
    const { productId, metadata = {} } = req.body;

    console.log('🛒 Creating checkout session for product:', { productId, metadata });

    // Validate input
    if (!productId) {
      return res.status(400).json({ 
        success: false, 
        error: 'Product ID is required' 
      });
    }

    // Get user token from headers
    const userToken = req.headers['x-whop-user-token'];
    if (!userToken) {
      return res.status(401).json({ 
        success: false, 
        error: 'No user token provided' 
      });
    }

    // Verify user token and get user ID
    const { userId } = await whopSdk.verifyUserToken(userToken);
    if (!userId) {
      return res.status(401).json({ 
        success: false, 
        error: 'Invalid user token' 
      });
    }

    console.log('✅ User verified:', { userId });

    // Create checkout session with product metadata
    const sessionMetadata = {
      ...metadata,
      userId,
      timestamp: new Date().toISOString(),
      source: 'trade_sensei_app'
    };

    const result = await whopSdk.payments.createCheckoutSession({
      planId: productId, // Use productId as planId for access passes
      metadata: sessionMetadata
    });

    if (!result?.id) {
      throw new Error("Failed to create checkout session");
    }

    console.log('✅ Checkout session created successfully:', {
      sessionId: result.id,
      planId: result.planId
    });

    res.json({
      success: true,
      data: {
        id: result.id,
        planId: result.planId
      }
    });

  } catch (error) {
    console.error('❌ Error creating checkout session:', error);
    
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to create checkout session',
      details: {
        timestamp: new Date().toISOString(),
        productId: req.body?.productId
      }
    });
  }
};
