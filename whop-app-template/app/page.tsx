export default function Page() {
	return (
		<div className="min-h-screen bg-[#0A0A0A] py-12 px-4 sm:px-6 lg:px-8">
			<div className="max-w-3xl mx-auto">
				<div className="text-center mb-12">
					<h1 className="text-4xl font-bold text-white mb-4">
						Osis Whop App
					</h1>
					<p className="text-lg text-white/60">
						AI-powered trading platform with hard paywall integration
					</p>
				</div>

				<div className="space-y-8">
					<div className="bg-[#1A1A1A] border border-white/[0.08] p-6 rounded-lg">
						<h2 className="text-xl font-semibold text-white mb-4 flex items-center">
							<span className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-blue-600 text-white mr-3">
								💳
							</span>
							Payment Modal Integration
						</h2>
						<p className="text-white/70 ml-11">
							This Whop app includes a hard paywall that requires users to pay $15 before accessing any features.
							The payment modal cannot be bypassed and blocks all functionality until payment is completed.
						</p>
					</div>

					<div className="bg-[#1A1A1A] border border-white/[0.08] p-6 rounded-lg">
						<h2 className="text-xl font-semibold text-white mb-4 flex items-center">
							<span className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-green-600 text-white mr-3">
								🔒
							</span>
							Security Features
						</h2>
						<p className="text-white/70 ml-11 mb-4">
							The payment modal includes several security features:
						</p>
						<ul className="text-white/60 ml-11 space-y-2 text-sm">
							<li>• Cannot be closed without completing payment</li>
							<li>• Blocks all app functionality until payment is verified</li>
							<li>• Uses Whop's secure payment processing</li>
							<li>• Persists payment state across sessions</li>
							<li>• Includes proper error handling and retry mechanisms</li>
						</ul>
					</div>

					<div className="bg-[#1A1A1A] border border-white/[0.08] p-6 rounded-lg">
						<h2 className="text-xl font-semibold text-white mb-4 flex items-center">
							<span className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-purple-600 text-white mr-3">
								🚀
							</span>
							How It Works
						</h2>
						<p className="text-white/70 ml-11 mb-4">
							When users access the Osis Whop app:
						</p>
						<ol className="text-white/60 ml-11 space-y-2 text-sm">
							<li>1. They are redirected to the main Osis app (app.osis.co)</li>
							<li>2. The payment modal appears immediately and cannot be bypassed</li>
							<li>3. Users must pay $15 to access any features</li>
							<li>4. After successful payment, they get full access to Osis</li>
							<li>5. Payment status is remembered across sessions</li>
						</ol>
					</div>
				</div>

				<div className="mt-12 text-center">
					<p className="text-white/50 text-sm">
						Need help? Visit{" "}
						<a
							href="https://app.osis.co"
							target="_blank"
							rel="noopener noreferrer"
							className="text-blue-400 hover:text-blue-300 underline"
						>
							app.osis.co
						</a>{" "}
						to see the payment modal in action
					</p>
				</div>
			</div>
		</div>
	);
}
