"use client";

import { useEffect } from "react";

export default function ExperiencePage({
	params,
}: {
	params: Promise<{ experienceId: string }>;
}) {
	useEffect(() => {
		// Redirect to the main Osis app with the experience ID
		// The main app will handle Whop authentication and payment modal
		const redirectToOsis = async () => {
			const { experienceId } = await params;

			// Construct the URL to the main Osis app
			const osisUrl = `https://app.osis.co/experiences/${experienceId}`;

			console.log('🔄 Redirecting to main Osis app:', osisUrl);

			// Redirect to the main app
			window.location.href = osisUrl;
		};

		redirectToOsis();
	}, [params]);

	return (
		<div className="flex justify-center items-center h-screen px-8 bg-[#0A0A0A] text-white">
			<div className="text-center space-y-4">
				<div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto">
					<div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-400"></div>
				</div>
				<h1 className="text-xl font-semibold">
					Redirecting to Osis...
				</h1>
				<p className="text-white/60 text-sm">
					You'll be redirected to the full Osis trading platform
				</p>
			</div>
		</div>
	);
}
