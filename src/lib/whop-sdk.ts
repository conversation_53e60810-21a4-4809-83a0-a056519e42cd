// Client-side Whop configuration and API client
// Note: This now uses the Whop intermediary server instead of direct SDK calls
// All Whop interactions go through the intermediary server

import { whopIntermediaryClient } from './whopIntermediaryClient';

// Helper function to check if we're running in a Whop context
export const isWhopContext = (): boolean => {
  if (typeof window !== 'undefined') {
    // Client-side check
    return window.location.pathname.startsWith('/experiences/') ||
           new URLSearchParams(window.location.search).has('whop') ||
           document.referrer.includes('whop.com');
  }
  return false;
};

// Helper function to get Whop user token from headers (for server-side)
export const getWhopUserToken = (headers: Headers): string | null => {
  return headers.get('x-whop-user-token');
};

// Client-side API functions that call the server-side Whop integration
export const whopApiClient = {
  // Get current user via intermediary server
  async getCurrentUser() {
    try {
      console.log('🔍 Fetching current Whop user via intermediary server...');

      const result = await whopIntermediaryClient.getCurrentUser();

      if (!result.success) {
        throw new Error(result.error || 'Failed to get current user');
      }

      return result.user;
    } catch (error) {
      console.error('❌ Error getting current Whop user:', error);
      throw error;
    }
  },

  // Legacy method for backward compatibility
  async verifyUserToken(_token?: string) {
    console.warn('⚠️ verifyUserToken is deprecated, use getCurrentUser() instead');
    return this.getCurrentUser();
  },

  // Check user access pass via direct API call
  async checkAccess(userId) {
    try {
      console.log('🔍 Checking access pass for user:', userId);

      // Call the check-access API endpoint directly
      const response = await fetch('/api/whop/check-access', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Forward Whop token if available
          ...(window.parent !== window && document.referrer.includes('whop.com') ? {
            'x-whop-user-token': localStorage.getItem('whop_user_token') || ''
          } : {})
        },
        body: JSON.stringify({ userId })
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to check access pass');
      }

      return result;
    } catch (error) {
      console.error('❌ Error checking Whop access pass:', error);
      throw error;
    }
  },

  // Debug function via intermediary server
  async debugHeaders() {
    try {
      console.log('🔍 Debug: Checking headers via intermediary server...');

      const result = await whopIntermediaryClient.debugHeaders();
      console.log('🔍 Debug headers result:', result);
      return result;
    } catch (error) {
      console.error('❌ Error debugging headers:', error);
      throw error;
    }
  },

  // Test connection via intermediary server
  async testConnection() {
    try {
      const result = await whopIntermediaryClient.testConnection();
      return result;
    } catch (error) {
      console.error('❌ Error testing Whop connection:', error);
      throw error;
    }
  },

  // Create Supabase user for Whop user via intermediary server
  async createSupabaseUser(accessResult?: any) {
    try {
      console.log('🔐 Creating Supabase user for current Whop user...');

      // First get the current Whop user via intermediary server
      const currentUser = await this.getCurrentUser();

      if (!currentUser) {
        throw new Error('No current Whop user found');
      }

      // Use the intermediary server to create the Supabase user with access level
      const result = await whopIntermediaryClient.createSupabaseUser(currentUser, accessResult);

      if (result.success && result.data) {
        console.log('✅ Successfully created Supabase user for Whop user');
        return {
          success: true,
          whopUser: currentUser,
          supabaseUser: result.data.user,
          credentials: result.data.credentials
        };
      } else {
        throw new Error(result.error || 'Failed to create Supabase user');
      }
    } catch (error) {
      console.error('❌ Error creating Supabase user:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
};

// Helper function to get environment variables safely
const getEnvVar = (key: string): string | undefined => {
  try {
    return (import.meta as any).env?.[key];
  } catch {
    return undefined;
  }
};

// Helper function to validate Whop configuration (client-side only checks public vars)
export const validateWhopConfig = (): boolean => {
  if (typeof window === 'undefined') return true; // Skip validation on server

  const requiredVars = [
    getEnvVar('VITE_WHOP_APP_ID'),
    getEnvVar('VITE_WHOP_AGENT_USER_ID'),
    getEnvVar('VITE_WHOP_COMPANY_ID')
  ];

  const isValid = requiredVars.every(variable => variable && variable.trim() !== '');

  if (!isValid) {
    console.warn('⚠️ Whop client configuration incomplete. Please check your environment variables.');
  }

  return isValid;
};

// Log configuration status on import (development only)
if (typeof window !== 'undefined' && getEnvVar('DEV')) {
  console.log('🔧 Whop Client Configuration:', {
    appId: getEnvVar('VITE_WHOP_APP_ID') ? '✅ Set' : '❌ Missing',
    agentUserId: getEnvVar('VITE_WHOP_AGENT_USER_ID') ? '✅ Set' : '❌ Missing',
    companyId: getEnvVar('VITE_WHOP_COMPANY_ID') ? '✅ Set' : '❌ Missing',
    isValid: validateWhopConfig()
  });
}
