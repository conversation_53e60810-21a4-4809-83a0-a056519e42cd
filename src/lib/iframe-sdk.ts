/**
 * Whop iframe SDK integration with fallback
 * Falls back to global SDK or mock implementation if official package unavailable
 */

// Get the appropriate app ID based on current path
const getAppId = () => {
  if (typeof window === 'undefined') return import.meta.env.VITE_WHOP_APP_ID;

  const path = window.location.pathname;
  if (path.includes('/trade')) {
    return import.meta.env.VITE_TRADING_WHOP_APP_ID || import.meta.env.VITE_WHOP_APP_ID;
  }
  return import.meta.env.VITE_WHOP_APP_ID;
};

// Fallback SDK implementation
const createFallbackSdk = () => {
  // Check if global whopIframeSdk is available (injected by Whop)
  if (typeof window !== 'undefined') {
    const globalSdk = (window as any)?.whopIframeSdk;
    if (globalSdk) {
      console.log('🔧 Using global whopIframeSdk');
      return globalSdk;
    }
  }

  // Create a mock SDK for development/testing
  console.warn('⚠️ No Whop SDK available, using mock implementation');
  return {
    inAppPurchase: async (data: any) => {
      console.log('🧪 Mock inAppPurchase called with:', data);
      return { status: 'ok', data: { success: true } };
    },
    openExternalUrl: (url: string) => {
      console.log('🧪 Mock openExternalUrl called with:', url);
      if (typeof window !== 'undefined') {
        window.open(url, '_blank');
      }
    }
  };
};

// Use fallback implementation for now
export const iframeSdk = createFallbackSdk();

console.log('🔧 Whop iframe SDK initialized:', {
  appId: getAppId(),
  timestamp: new Date().toISOString(),
  windowContext: typeof window !== 'undefined' ? {
    isInIframe: window.parent !== window,
    origin: window.location.origin,
    pathname: window.location.pathname,
    referrer: document.referrer
  } : 'SSR'
});

// Check if SDK is properly initialized
console.log('🔧 Whop iframe SDK status:', {
  sdkExists: !!iframeSdk,
  hasInAppPurchase: !!(iframeSdk as any)?.inAppPurchase,
  hasOpenExternalUrl: !!(iframeSdk as any)?.openExternalUrl,
  sdkMethods: iframeSdk ? Object.keys(iframeSdk) : []
});
