// TradeOff Design System
// Premium dark theme with teal accent and professional depth

export const colors = {
  // Primary Accent System
  primary: {
    main: '#14B8A6',      // Primary teal-green accent
    hover: '#1FD3C0',     // Slightly brighter for hover states
    active: '#109186',    // Slightly darker for active/pressed
    disabled: 'rgba(20, 184, 166, 0.4)', // Disabled state
  },

  // Functional Colors
  functional: {
    profit: '#22C55E',    // Profit/Positive actions
    loss: '#F43F5E',      // Loss/Negative actions  
    neutral: '#94A3B8',   // Neutral/Info states
  },

  // Background Layers
  background: {
    base: '#0B0B0B',      // Main app background
    surface1: '#151515',  // Cards, panels
    surface2: '#1E1E1E',  // Hovered/active surfaces
    border: 'rgba(255, 255, 255, 0.05)', // Borders and dividers
  },

  // Text Hierarchy
  text: {
    primary: '#FFFFFF',   // Headlines (H1/H2)
    secondary: '#E5E5E5', // Section titles (H3)
    body: '#B0B0B0',      // Body copy
    numeric: '#14B8A6',   // Numeric data (profits, prices) - uses primary teal
  },

  // State Overlays
  overlay: {
    hover: 'rgba(255, 255, 255, 0.05)',
    active: 'rgba(255, 255, 255, 0.1)',
    disabled: 'rgba(255, 255, 255, 0.02)',
  }
};

export const shadows = {
  // Subtle depth system
  card: '0 1px 4px rgba(0, 0, 0, 0.4)',
  floating: '0 4px 12px rgba(0, 0, 0, 0.6)',
  
  // Brand glow effects (use sparingly)
  tealGlow: {
    subtle: '0 0 8px rgba(20, 184, 166, 0.1)',
    medium: '0 0 16px rgba(20, 184, 166, 0.2)',
    strong: '0 0 24px rgba(20, 184, 166, 0.3)',
  }
};

export const typography = {
  // Font weights
  weights: {
    regular: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  },

  // Line heights
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
  },

  // Font sizes (in rem)
  sizes: {
    xs: '0.75rem',   // 12px
    sm: '0.875rem',  // 14px
    base: '1rem',    // 16px
    lg: '1.125rem',  // 18px
    xl: '1.25rem',   // 20px
    '2xl': '1.5rem', // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem',  // 36px
  }
};

export const spacing = {
  // 8pt grid system - all values in rem
  xs: '0.5rem',   // 8px
  sm: '0.75rem',  // 12px
  md: '1rem',     // 16px
  lg: '1.25rem',  // 20px
  xl: '1.5rem',   // 24px
  '2xl': '2rem',  // 32px
  '3xl': '3rem',  // 48px
  '4xl': '4rem',  // 64px
};

export const borderRadius = {
  sm: '0.25rem',   // 4px
  md: '0.375rem',  // 6px
  lg: '0.5rem',    // 8px
  xl: '0.75rem',   // 12px
  '2xl': '1rem',   // 16px
  full: '9999px',  // Fully rounded
};

export const transitions = {
  // Consistent timing
  fast: '200ms ease-out',
  normal: '250ms ease-out',
  slow: '300ms ease-out',
  
  // Interaction feedback
  press: {
    scale: 'transform 150ms ease-out',
    shadow: 'box-shadow 150ms ease-out',
  }
};

export const layout = {
  // Panel padding
  panelPadding: {
    sm: spacing.md,  // 16px
    md: spacing.lg,  // 20px
    lg: spacing.xl,  // 24px
  },

  // Card dimensions
  cardMinHeight: '120px',
  cardMaxWidth: '400px',
  
  // Chart spacing
  chartPadding: {
    horizontal: spacing.xl, // 24px
    vertical: spacing.lg,   // 20px
  }
};

// Component-specific design tokens
export const components = {
  button: {
    height: {
      sm: '32px',
      md: '40px',
      lg: '48px',
    },
    padding: {
      sm: `${spacing.xs} ${spacing.md}`, // 8px 16px
      md: `${spacing.sm} ${spacing.lg}`, // 12px 20px
      lg: `${spacing.md} ${spacing.xl}`, // 16px 24px
    }
  },

  card: {
    padding: spacing.lg, // 20px
    borderRadius: borderRadius.lg,
    background: colors.background.surface1,
    border: `1px solid ${colors.background.border}`,
    boxShadow: shadows.card,
  },

  input: {
    height: '40px',
    padding: `${spacing.sm} ${spacing.md}`,
    borderRadius: borderRadius.md,
    background: colors.background.surface2,
    border: `1px solid ${colors.background.border}`,
  }
};

// Utility functions for consistent styling
export const utils = {
  // Generate hover state
  hover: (baseColor: string, opacity: number = 0.8) => 
    baseColor.includes('rgba') 
      ? baseColor.replace(/[\d.]+\)$/, `${opacity})`)
      : `${baseColor}${Math.round(opacity * 255).toString(16)}`,

  // Generate pressed state  
  pressed: (scale: number = 0.97) => ({
    transform: `scale(${scale})`,
    transition: transitions.press.scale,
  }),

  // Generate glow effect
  glow: (color: string, intensity: 'subtle' | 'medium' | 'strong' = 'subtle') => ({
    boxShadow: `0 0 ${intensity === 'subtle' ? '8px' : intensity === 'medium' ? '16px' : '24px'} ${color}`,
  }),
};

export default {
  colors,
  shadows,
  typography,
  spacing,
  borderRadius,
  transitions,
  layout,
  components,
  utils,
};
