import React, { useState } from 'react';
import { ChevronDown, Filter, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { type AgentCategory, type DiscoverFilters } from '@/services/discoverService';

interface MobileFiltersDropdownProps {
  categories: AgentCategory[];
  filters: DiscoverFilters;
  onFilterChange: (filters: Partial<DiscoverFilters>) => void;
}

const MobileFiltersDropdown: React.FC<MobileFiltersDropdownProps> = ({ 
  categories, 
  filters, 
  onFilterChange 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>(filters.tags || []);

  // Common tags for filtering
  const commonTags = [
    'RSI', 'MACD', 'Moving Average', 'Bollinger Bands', 'Support/Resistance',
    'Breakout', 'Momentum', 'Volume', 'Candlestick', 'Trend Following',
    'Mean Reversion', 'Scalping', 'Position Sizing', 'Stop Loss', 'Take Profit'
  ];

  const handleCategoryChange = (category: string, checked: boolean) => {
    if (checked) {
      onFilterChange({ category });
    } else {
      onFilterChange({ category: '' });
    }
  };

  const handleTagChange = (tag: string, checked: boolean) => {
    let newTags: string[];
    if (checked) {
      newTags = [...selectedTags, tag];
    } else {
      newTags = selectedTags.filter(t => t !== tag);
    }
    setSelectedTags(newTags);
    onFilterChange({ tags: newTags });
  };

  const handleFeaturedChange = (checked: boolean) => {
    onFilterChange({ featured: checked || undefined });
  };

  const clearAllFilters = () => {
    setSelectedTags([]);
    onFilterChange({
      category: '',
      tags: [],
      featured: undefined
    });
  };

  const hasActiveFilters = filters.category || (filters.tags && filters.tags.length > 0) || filters.featured;
  const activeFilterCount = (filters.category ? 1 : 0) + (filters.tags?.length || 0) + (filters.featured ? 1 : 0);

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button
          variant="outline"
          className="w-full h-10 bg-white/[0.02] border-white/[0.08] text-white hover:border-white/[0.12] focus:border-white/[0.15] transition-all duration-200 rounded-md justify-between"
          style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
        >
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4" />
            <span>Filters</span>
            {activeFilterCount > 0 && (
              <Badge className="bg-green-500/20 text-green-400 border border-green-500/30 text-xs h-5 px-1.5">
                {activeFilterCount}
              </Badge>
            )}
          </div>
          <ChevronDown className="w-4 h-4" />
        </Button>
      </SheetTrigger>
      
      <SheetContent 
        side="bottom" 
        className="bg-[#0A0A0A] border-t border-white/[0.08] max-h-[80vh] overflow-y-auto"
      >
        <SheetHeader className="pb-4">
          <div className="flex items-center justify-between">
            <SheetTitle className="text-lg text-white flex items-center gap-2">
              <Filter className="w-5 h-5" />
              Filters
            </SheetTitle>
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="text-white/50 hover:text-white"
              >
                <X className="w-4 h-4 mr-1" />
                Clear All
              </Button>
            )}
          </div>
        </SheetHeader>

        <div className="space-y-6 pb-6">
          {/* Quick Filters */}
          <div>
            <h4 className="text-sm font-medium text-white/70 mb-3">Quick Filters</h4>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="featured-mobile"
                checked={filters.featured || false}
                onCheckedChange={handleFeaturedChange}
                className="border-white/[0.12] data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
              />
              <label htmlFor="featured-mobile" className="text-sm text-white/70 cursor-pointer">
                Featured Agents
              </label>
            </div>
          </div>

          {/* Categories */}
          <div>
            <h4 className="text-sm font-medium text-white/70 mb-3">Categories</h4>
            <div className="grid grid-cols-2 gap-3">
              {categories.map((category) => (
                <div key={category.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`category-mobile-${category.id}`}
                    checked={filters.category === category.name}
                    onCheckedChange={(checked) => handleCategoryChange(category.name, checked as boolean)}
                    className="border-white/[0.12] data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                  />
                  <label
                    htmlFor={`category-mobile-${category.id}`}
                    className="text-sm text-white/70 cursor-pointer flex-1"
                  >
                    {category.name}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Tags */}
          <div>
            <h4 className="text-sm font-medium text-white/70 mb-3">Tags</h4>
            <div className="grid grid-cols-2 gap-3 max-h-48 overflow-y-auto">
              {commonTags.map((tag) => (
                <div key={tag} className="flex items-center space-x-2">
                  <Checkbox
                    id={`tag-mobile-${tag}`}
                    checked={selectedTags.includes(tag)}
                    onCheckedChange={(checked) => handleTagChange(tag, checked as boolean)}
                    className="border-white/[0.12] data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                  />
                  <label
                    htmlFor={`tag-mobile-${tag}`}
                    className="text-sm text-white/70 cursor-pointer flex-1"
                  >
                    {tag}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Active Filters Summary */}
          {hasActiveFilters && (
            <div>
              <h4 className="text-sm font-medium text-white/70 mb-3">Active Filters</h4>
              <div className="flex flex-wrap gap-2">
                {filters.featured && (
                  <Badge className="bg-green-500/20 text-green-400 border border-green-500/30">
                    Featured
                  </Badge>
                )}
                {filters.category && (
                  <Badge variant="outline" className="border-white/[0.12] text-white/70 bg-white/[0.04]">
                    {filters.category}
                  </Badge>
                )}
                {filters.tags && filters.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="border-white/[0.12] text-white/70 bg-white/[0.04]">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Apply Button */}
          <div className="pt-4 border-t border-white/[0.08]">
            <Button
              onClick={() => setIsOpen(false)}
              className="w-full bg-white text-black hover:bg-white/90 transition-all duration-200"
            >
              Apply Filters
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default MobileFiltersDropdown;
