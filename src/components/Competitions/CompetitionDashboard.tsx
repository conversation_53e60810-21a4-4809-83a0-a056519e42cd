import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Trophy, Timer, Users, DollarSign, TrendingUp, Calendar, ArrowLeft, TrendingDown, Search, Filter, SortDesc, Target, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import CompetitionDiscovery from './CompetitionDiscovery';
import CompetitionLeaderboard from './CompetitionLeaderboard';
import CompetitionCreator from './CompetitionCreator';
import CompetitionCreatorHub from './CompetitionCreatorHub';
import CompetitionAnalytics from './CompetitionAnalytics';
import { useCompetitions, Competition, LeaderboardEntry } from '@/hooks/useCompetitions';
import { formatDistanceToNow, isAfter, isBefore } from 'date-fns';

interface CompetitionDashboardProps {
  onClose?: () => void;
}



const CompetitionDashboard: React.FC<CompetitionDashboardProps> = ({ onClose }) => {
  const { userCompetitions, refreshCompetitions, refreshUserCompetitions, competitions } = useCompetitions();
  const [selectedCompetition, setSelectedCompetition] = useState<Competition | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'open' | 'active' | 'completed'>('all');
  const [sortBy, setSortBy] = useState<'newest' | 'participants' | 'prize'>('newest');
  const [showCreatorHub, setShowCreatorHub] = useState(false);
  const [showAnalytics, setShowAnalytics] = useState(false);

  // Auto-refresh data every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      refreshCompetitions();
      refreshUserCompetitions();
    }, 30000);

    return () => clearInterval(interval);
  }, [refreshCompetitions, refreshUserCompetitions]);

  const getTimeRemaining = (competition: Competition) => {
    const now = new Date();
    const start = new Date(competition.competition_start);
    const end = new Date(competition.competition_end);

    if (isBefore(now, start)) {
      return {
        label: 'Starts in',
        time: formatDistanceToNow(start),
        status: 'upcoming' as const
      };
    } else if (isAfter(now, start) && isBefore(now, end)) {
      return {
        label: 'Ends in',
        time: formatDistanceToNow(end),
        status: 'active' as const
      };
    } else {
      return {
        label: 'Ended',
        time: formatDistanceToNow(end) + ' ago',
        status: 'completed' as const
      };
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'active': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'completed': return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  // Show analytics view
  if (showAnalytics) {
    return (
      <CompetitionAnalytics
        onBack={() => setShowAnalytics(false)}
      />
    );
  }

  if (selectedCompetition) {
    const timeInfo = getTimeRemaining(selectedCompetition);
    
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="h-full bg-[#0A0A0A] text-white overflow-hidden"
      >
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="flex-shrink-0 p-6 border-b border-white/[0.08]">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedCompetition(null)}
                  className="text-white/60 hover:text-white"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back
                </Button>
                <div>
                  <h1 className="text-2xl font-bold text-white flex items-center gap-2">
                    <Trophy className="w-6 h-6 text-yellow-400" />
                    {selectedCompetition.name}
                  </h1>
                  <p className="text-white/60 mt-1">
                    {selectedCompetition.description || 'No description available'}
                  </p>
                </div>
              </div>
              {onClose && (
                <Button variant="ghost" onClick={onClose} className="text-white/60 hover:text-white">
                  ✕
                </Button>
              )}
            </div>

            {/* Competition Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
              <Card className="bg-white/[0.02] border-white/[0.08]">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Timer className="w-4 h-4 text-blue-400" />
                    <div>
                      <p className="text-white/60 text-sm">{timeInfo.label}</p>
                      <p className="text-white font-semibold">{timeInfo.time}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/[0.02] border-white/[0.08]">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4 text-green-400" />
                    <div>
                      <p className="text-white/60 text-sm">Starting Balance</p>
                      <p className="text-white font-semibold">
                        ${selectedCompetition.starting_balance.toLocaleString()}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/[0.02] border-white/[0.08]">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4 text-purple-400" />
                    <div>
                      <p className="text-white/60 text-sm">Participants</p>
                      <p className="text-white font-semibold">
                        {selectedCompetition.participant_count || 0}
                        {selectedCompetition.max_participants && ` / ${selectedCompetition.max_participants}`}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/[0.02] border-white/[0.08]">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(timeInfo.status)}>
                      {selectedCompetition.status}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>


        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -30 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className="min-h-screen bg-[#0A0A0A] text-white"
    >
      {/* Simple Top Navbar */}
      <div className="w-full bg-[#0A0A0A] border-b border-white/[0.08] px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <img
              src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/tradeoff//ChatGPT%20Image%20Jul%2023,%202025,%2008_29_13%20PM.png"
              alt="TradeOff Logo"
              className="w-10 h-10"
            />
          </div>

          <div className="flex items-center gap-8">
            {/* Navigation - Top Right */}
            <div className="flex items-center gap-6">
              <button
                onClick={() => {
                  // Close competitions and return to chart
                  setShowAnalytics(false);
                  if (onClose) onClose();
                }}
                className="flex items-center gap-2 text-white/60 hover:text-white transition-colors group"
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
              >
                <img
                  src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/tradeoff//home.png"
                  alt="Chart"
                  className="w-4 h-4 opacity-60 group-hover:opacity-100 transition-opacity"
                  style={{ filter: 'brightness(0) saturate(100%) invert(100%)' }}
                />
                <span className="text-sm">Chart</span>
              </button>
              <button
                className="flex items-center gap-2 text-white font-medium hover:text-white/80 transition-colors"
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
              >
                <img
                  src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/tradeoff//Globe%20(1).png"
                  alt="Competitions"
                  className="w-4 h-4 opacity-100"
                  style={{ filter: 'brightness(0) saturate(100%) invert(100%)' }}
                />
                <span className="text-sm">Competitions</span>
              </button>
              <button
                onClick={() => setShowAnalytics(true)}
                className="flex items-center gap-2 text-white/60 hover:text-white transition-colors group"
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
              >
                <img
                  src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/tradeoff//Bar%20Chart%201.png"
                  alt="Analytics"
                  className="w-4 h-4 opacity-60 group-hover:opacity-100 transition-opacity"
                  style={{ filter: 'brightness(0) saturate(100%) invert(100%)' }}
                />
                <span className="text-sm">Analytics</span>
              </button>
            </div>

            {/* Create Competition Button - Turquoise Highlighted */}
            <button
              onClick={() => setShowCreatorHub(true)}
              className="flex items-center gap-3 bg-teal-500/[0.15] hover:bg-teal-500/[0.25] border border-teal-500/[0.4] hover:border-teal-500/[0.6] text-teal-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-[inset_0_1px_0_rgba(20,184,166,0.15)] hover:shadow-[inset_0_1px_0_rgba(20,184,166,0.25)] backdrop-blur-sm active:scale-[0.98]"
              style={{
                fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                boxShadow: '0 0 20px rgba(20, 184, 166, 0.15), inset 0 1px 0 rgba(20, 184, 166, 0.15)'
              }}
            >
              <span>Create Competition</span>
              <div className="flex items-center gap-1">
                <kbd className="px-2 py-1 text-xs font-semibold text-teal-300/80 bg-teal-500/[0.1] border border-teal-500/[0.3] rounded shadow-sm min-w-[20px] h-[20px] flex items-center justify-center">
                  ⌘
                </kbd>
                <kbd className="px-2 py-1 text-xs font-semibold text-teal-300/80 bg-teal-500/[0.1] border border-teal-500/[0.3] rounded shadow-sm min-w-[20px] h-[20px] flex items-center justify-center">
                  K
                </kbd>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Subtle background gradient */}
      <div
        className="absolute inset-0 pointer-events-none opacity-30 top-16"
        style={{
          background: 'linear-gradient(135deg, rgba(255,255,255,0.008) 0%, rgba(255,255,255,0.003) 50%, transparent 100%)'
        }}
      />

      {/* Content Container with Better Spacing */}
      <div className="max-w-7xl mx-auto px-8 py-16 relative z-10">

        {/* Enhanced Title Section */}
        <div className="text-center mb-12 mt-16">
          <h1 className="text-4xl font-semibold text-white tracking-tight mb-4" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
            Discover Trading Competitions
          </h1>
          <p className="text-white/60 text-lg max-w-2xl mx-auto leading-relaxed font-medium">Join competitions or create your own</p>
        </div>

        {/* Beautiful Search Bar with Strong Teal Glow */}
        <div className="flex justify-center mb-8">
          <div className="w-full max-w-lg">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <Search className="w-5 h-5 text-[#14B8A6]/80" />
              </div>
              <input
                type="text"
                placeholder="Search competitions, strategies, traders..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-4 bg-gradient-to-br from-[#151515]/90 to-[#0B0B0B]/70 border-2 border-[#14B8A6]/30 rounded-xl text-white placeholder-white/50 focus:border-[#14B8A6]/60 focus:outline-none focus:ring-0 shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] transition-all duration-300 backdrop-blur-md text-sm font-medium"
                style={{
                  fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                  boxShadow: '0 0 30px rgba(20, 184, 166, 0.2), 0 0 60px rgba(20, 184, 166, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                }}
                onFocus={(e) => {
                  e.target.style.boxShadow = '0 0 40px rgba(20, 184, 166, 0.4), 0 0 80px rgba(20, 184, 166, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.15)';
                }}
                onBlur={(e) => {
                  e.target.style.boxShadow = '0 0 30px rgba(20, 184, 166, 0.2), 0 0 60px rgba(20, 184, 166, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)';
                }}
              />
            </div>
          </div>
        </div>

        {/* Filter Toggles with Better Spacing */}
        <div className="flex justify-center items-center gap-6 mb-20">
        {/* Status Filter */}
        <div className="flex items-center gap-2">
          <span className="text-white/60 text-sm">Status:</span>
          <div className="flex gap-1">
            {['all', 'open', 'active', 'completed'].map((status) => (
              <button
                key={status}
                onClick={() => setStatusFilter(status as any)}
                className={`px-3 py-1 text-xs rounded-full transition-all duration-200 ${
                  statusFilter === status
                    ? 'bg-white/20 text-white border border-white/30'
                    : 'bg-white/5 text-white/60 border border-white/10 hover:bg-white/10 hover:text-white/80'
                }`}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Sort Filter */}
        <div className="flex items-center gap-2">
          <span className="text-white/60 text-sm">Sort:</span>
          <div className="flex gap-1">
            {[
              { key: 'newest', label: 'Newest' },
              { key: 'participants', label: 'Popular' },
              { key: 'prize', label: 'Prize' }
            ].map((option) => (
              <button
                key={option.key}
                onClick={() => setSortBy(option.key as any)}
                className={`px-3 py-1 text-xs rounded-full transition-all duration-200 ${
                  sortBy === option.key
                    ? 'bg-white/20 text-white border border-white/30'
                    : 'bg-white/5 text-white/60 border border-white/10 hover:bg-white/10 hover:text-white/80'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Competition Grid - Centered and Enhanced */}
      <div className="flex-1 w-full max-w-6xl mx-auto px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8 justify-center">
          {/* Competition Card 1 - Weekly Crypto Challenge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="bg-white/[0.02] border border-white/[0.08] rounded-xl p-6 hover:border-white/[0.15] hover:bg-white/[0.04] transition-all duration-200 cursor-pointer group"
          >
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="text-white font-semibold text-lg" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                    Weekly Crypto Challenge
                  </h3>
                  <span className="px-2 py-1 rounded-lg text-xs font-medium bg-green-500/20 text-green-400 border-green-500/30">
                    Active
                  </span>
                </div>
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-teal-400 text-xs font-medium px-2 py-1 bg-teal-500/[0.1] rounded-md">
                    Crypto Trading
                  </span>
                  <span className="text-white/50 text-xs">7 days</span>
                </div>
                <p className="text-white/60 text-sm line-clamp-2">
                  Trade the top 10 cryptocurrencies and compete for the highest returns this week.
                </p>
              </div>
            </div>

            {/* Prize & Entry Info */}
            <div className="grid grid-cols-2 gap-3 mb-4">
              <div className="bg-white/[0.03] rounded-lg p-3 border border-white/[0.05]">
                <div className="text-white/60 text-xs mb-1">Prize Pool</div>
                <div className="text-green-400 font-bold text-lg">$5,000</div>
              </div>
              <div className="bg-white/[0.03] rounded-lg p-3 border border-white/[0.05]">
                <div className="text-white/60 text-xs mb-1">Entry Fee</div>
                <div className="text-white font-bold text-lg">$50</div>
              </div>
            </div>

            {/* Participants & Mini Leaderboard */}
            <div className="mb-4">
              <div className="flex items-center justify-between mb-3">
                <span className="text-white/60 text-sm">Participants</span>
                <span className="text-white font-medium">127</span>
              </div>

              {/* Mini Leaderboard */}
              <div className="bg-white/[0.02] rounded-lg p-3 border border-white/[0.05]">
                <div className="text-white/60 text-xs mb-2 flex items-center gap-1">
                  <Trophy className="w-3 h-3" />
                  Top Performers
                </div>
                <div className="space-y-1">
                  {[
                    { name: 'CryptoKing', return: '+24.5%' },
                    { name: 'BlockchainBull', return: '+18.2%' },
                    { name: 'DeFiDegen', return: '+15.7%' }
                  ].map((trader, idx) => (
                    <div key={idx} className="flex items-center justify-between text-xs">
                      <div className="flex items-center gap-2">
                        <span className="text-white/40 w-3">{idx + 1}.</span>
                        <span className="text-white/80">{trader.name}</span>
                      </div>
                      <span className="text-green-400 font-medium">{trader.return}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Action Button */}
            <button className="w-full bg-teal-500/[0.08] hover:bg-teal-500/[0.15] border border-teal-500/[0.2] hover:border-teal-500/[0.4] text-teal-400 py-3 rounded-lg text-sm font-medium transition-all duration-200">
              Join Competition
            </button>
          </motion.div>

          {/* Competition Card 2 - S&P 500 Masters */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
            className="bg-white/[0.02] border border-white/[0.08] rounded-xl p-6 hover:border-white/[0.15] hover:bg-white/[0.04] transition-all duration-200 cursor-pointer group"
          >
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="text-white font-semibold text-lg" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                    S&P 500 Masters
                  </h3>
                  <span className="px-2 py-1 rounded-lg text-xs font-medium bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                    Open
                  </span>
                </div>
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-teal-400 text-xs font-medium px-2 py-1 bg-teal-500/[0.1] rounded-md">
                    Stock Trading
                  </span>
                  <span className="text-white/50 text-xs">30 days</span>
                </div>
                <p className="text-white/60 text-sm line-clamp-2">
                  Focus on large-cap stocks from the S&P 500. Show your stock picking skills.
                </p>
              </div>
            </div>

            {/* Prize & Entry Info */}
            <div className="grid grid-cols-2 gap-3 mb-4">
              <div className="bg-white/[0.03] rounded-lg p-3 border border-white/[0.05]">
                <div className="text-white/60 text-xs mb-1">Prize Pool</div>
                <div className="text-green-400 font-bold text-lg">$10,000</div>
              </div>
              <div className="bg-white/[0.03] rounded-lg p-3 border border-white/[0.05]">
                <div className="text-white/60 text-xs mb-1">Entry Fee</div>
                <div className="text-white font-bold text-lg">$100</div>
              </div>
            </div>

            {/* Participants */}
            <div className="mb-4">
              <div className="flex items-center justify-between mb-3">
                <span className="text-white/60 text-sm">Participants</span>
                <span className="text-white font-medium">89</span>
              </div>

              {/* Registration Info */}
              <div className="bg-white/[0.02] rounded-lg p-3 border border-white/[0.05]">
                <div className="text-white/60 text-xs mb-1">Starts in</div>
                <div className="text-white font-medium">1 day</div>
              </div>
            </div>

            {/* Action Button */}
            <button className="w-full bg-teal-500/[0.08] hover:bg-teal-500/[0.15] border border-teal-500/[0.2] hover:border-teal-500/[0.4] text-teal-400 py-3 rounded-lg text-sm font-medium transition-all duration-200">
              Register Now
            </button>
          </motion.div>

          {/* Competition Card 3 - Forex Fury */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
            className="bg-white/[0.02] border border-white/[0.08] rounded-xl p-6 hover:border-white/[0.15] hover:bg-white/[0.04] transition-all duration-200 cursor-pointer group"
          >
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="text-white font-semibold text-lg" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                    Forex Fury
                  </h3>
                  <span className="px-2 py-1 rounded-lg text-xs font-medium bg-green-500/20 text-green-400 border-green-500/30">
                    Active
                  </span>
                </div>
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-teal-400 text-xs font-medium px-2 py-1 bg-teal-500/[0.1] rounded-md">
                    Forex Trading
                  </span>
                  <span className="text-white/50 text-xs">14 days</span>
                </div>
                <p className="text-white/60 text-sm line-clamp-2">
                  Currency pairs trading competition. EUR/USD, GBP/USD, and more major pairs.
                </p>
              </div>
            </div>

            {/* Prize & Entry Info */}
            <div className="grid grid-cols-2 gap-3 mb-4">
              <div className="bg-white/[0.03] rounded-lg p-3 border border-white/[0.05]">
                <div className="text-white/60 text-xs mb-1">Prize Pool</div>
                <div className="text-green-400 font-bold text-lg">$7,500</div>
              </div>
              <div className="bg-white/[0.03] rounded-lg p-3 border border-white/[0.05]">
                <div className="text-white/60 text-xs mb-1">Entry Fee</div>
                <div className="text-white font-bold text-lg">$75</div>
              </div>
            </div>

            {/* Participants & Mini Leaderboard */}
            <div className="mb-4">
              <div className="flex items-center justify-between mb-3">
                <span className="text-white/60 text-sm">Participants</span>
                <span className="text-white font-medium">156</span>
              </div>

              {/* Mini Leaderboard */}
              <div className="bg-white/[0.02] rounded-lg p-3 border border-white/[0.05]">
                <div className="text-white/60 text-xs mb-2 flex items-center gap-1">
                  <Trophy className="w-3 h-3" />
                  Top Performers
                </div>
                <div className="space-y-1">
                  {[
                    { name: 'ForexPhantom', return: '+19.8%' },
                    { name: 'CurrencyKing', return: '+17.3%' },
                    { name: 'PipMaster', return: '+14.9%' }
                  ].map((trader, idx) => (
                    <div key={idx} className="flex items-center justify-between text-xs">
                      <div className="flex items-center gap-2">
                        <span className="text-white/40 w-3">{idx + 1}.</span>
                        <span className="text-white/80">{trader.name}</span>
                      </div>
                      <span className="text-green-400 font-medium">{trader.return}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Action Button */}
            <button className="w-full bg-teal-500/[0.08] hover:bg-teal-500/[0.15] border border-teal-500/[0.2] hover:border-teal-500/[0.4] text-teal-400 py-3 rounded-lg text-sm font-medium transition-all duration-200">
              Join Competition
            </button>
          </motion.div>

          {/* Competition Card 4 - Options Olympiad */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.4 }}
            className="bg-white/[0.02] border border-white/[0.08] rounded-xl p-6 hover:border-white/[0.15] hover:bg-white/[0.04] transition-all duration-200 cursor-pointer group"
          >
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="text-white font-semibold text-lg" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                    Options Olympiad
                  </h3>
                  <span className="px-2 py-1 rounded-lg text-xs font-medium bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                    Open
                  </span>
                </div>
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-teal-400 text-xs font-medium px-2 py-1 bg-teal-500/[0.1] rounded-md">
                    Options Trading
                  </span>
                  <span className="text-white/50 text-xs">21 days</span>
                </div>
                <p className="text-white/60 text-sm line-clamp-2">
                  Advanced options strategies competition. Calls, puts, spreads, and complex strategies.
                </p>
              </div>
            </div>

            {/* Prize & Entry Info */}
            <div className="grid grid-cols-2 gap-3 mb-4">
              <div className="bg-white/[0.03] rounded-lg p-3 border border-white/[0.05]">
                <div className="text-white/60 text-xs mb-1">Prize Pool</div>
                <div className="text-green-400 font-bold text-lg">$15,000</div>
              </div>
              <div className="bg-white/[0.03] rounded-lg p-3 border border-white/[0.05]">
                <div className="text-white/60 text-xs mb-1">Entry Fee</div>
                <div className="text-white font-bold text-lg">$200</div>
              </div>
            </div>

            {/* Participants */}
            <div className="mb-4">
              <div className="flex items-center justify-between mb-3">
                <span className="text-white/60 text-sm">Participants</span>
                <span className="text-white font-medium">67</span>
              </div>

              {/* Registration Info */}
              <div className="bg-white/[0.02] rounded-lg p-3 border border-white/[0.05]">
                <div className="text-white/60 text-xs mb-1">Starts in</div>
                <div className="text-white font-medium">3 days</div>
              </div>
            </div>

            {/* Action Button */}
            <button className="w-full bg-teal-500/[0.08] hover:bg-teal-500/[0.15] border border-teal-500/[0.2] hover:border-teal-500/[0.4] text-teal-400 py-3 rounded-lg text-sm font-medium transition-all duration-200">
              Register Now
            </button>
          </motion.div>
        </div>

        {/* Show More */}
        <div className="text-center pb-8">
          <button className="text-white/60 text-sm hover:text-white/80 transition-colors">
            Show more competitions
          </button>
        </div>
      </div>

      {/* Competition Creator Hub Modal */}
      {showCreatorHub && (
        <CompetitionCreatorHub
          isOpen={showCreatorHub}
          onClose={() => setShowCreatorHub(false)}
          onCompetitionCreated={() => {
            setShowCreatorHub(false);
            refreshCompetitions();
            refreshUserCompetitions();
          }}
        />
      )}
      </div>
    </motion.div>
  );
};

export default CompetitionDashboard;
