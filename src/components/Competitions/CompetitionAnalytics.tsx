import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Trophy, TrendingUp, Users, DollarSign, Calendar, ArrowLeft, Eye, Target, Award, Crown } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useCompetitions, Competition } from '@/hooks/useCompetitions';
import { useAuth } from '@/contexts/AuthContext';

interface CompetitionAnalyticsProps {
  onBack: () => void;
}

const CompetitionAnalytics: React.FC<CompetitionAnalyticsProps> = ({ onBack }) => {
  const { user } = useAuth();
  const { competitions, userCompetitions } = useCompetitions();
  const [selectedCompetition, setSelectedCompetition] = useState<Competition | null>(null);

  // Sample created competitions for analytics demo
  const sampleCreatedCompetitions = [
    {
      id: 'created-1',
      name: 'My Crypto Challenge',
      entry_fee: 50,
      participant_count: 127,
      status: 'active',
      prize_pool: 5000,
      revenue: 6350
    },
    {
      id: 'created-2',
      name: 'Stock Picking Contest',
      entry_fee: 100,
      participant_count: 89,
      status: 'completed',
      prize_pool: 8000,
      revenue: 8900
    },
    {
      id: 'created-3',
      name: 'Options Mastery',
      entry_fee: 200,
      participant_count: 45,
      status: 'active',
      prize_pool: 8000,
      revenue: 9000
    },
    {
      id: 'created-4',
      name: 'Forex Championship',
      entry_fee: 75,
      participant_count: 156,
      status: 'completed',
      prize_pool: 10000,
      revenue: 11700
    }
  ];

  // Get user's created competitions (use sample data for demo)
  const myCreatedCompetitions = competitions.filter(comp => comp.creator_id === user?.id);
  const allCreatedCompetitions = myCreatedCompetitions.length > 0 ? myCreatedCompetitions : sampleCreatedCompetitions;

  // Calculate analytics with enhanced sample data
  const totalRevenue = allCreatedCompetitions.reduce((sum, comp) => sum + ((comp as any).revenue || (comp.entry_fee || 0) * (comp.participant_count || 0)), 0);
  const totalParticipants = allCreatedCompetitions.reduce((sum, comp) => sum + (comp.participant_count || 0), 0);
  const activeCompetitions = allCreatedCompetitions.filter(comp => comp.status === 'active').length;
  const completedCompetitions = allCreatedCompetitions.filter(comp => comp.status === 'completed').length;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'active': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'completed': return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -30 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className="h-full bg-[#0A0A0A] text-white overflow-hidden relative"
    >
      {/* Subtle radial glow background */}
      <div
        className="absolute inset-0 pointer-events-none"
        style={{
          background: 'radial-gradient(circle at center, rgba(255,255,255,0.025) 0%, rgba(255,255,255,0.012) 35%, rgba(255,255,255,0.004) 60%, transparent 80%)',
          filter: 'blur(0.5px)'
        }}
      />
      
      {/* Simple Top Navbar */}
      <div className="w-full bg-[#0A0A0A] border-b border-white/[0.08] px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <img
              src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/tradeoff//ChatGPT%20Image%20Jul%2023,%202025,%2008_29_13%20PM.png"
              alt="TradeOff Logo"
              className="w-10 h-10"
            />
          </div>

          <div className="flex items-center gap-8">
            {/* Navigation - Top Right */}
            <div className="flex items-center gap-6">
              <button
                onClick={() => {
                  // Go back to chart (close all competition modals)
                  onBack();
                }}
                className="flex items-center gap-2 text-white/60 hover:text-white transition-colors group"
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
              >
                <img
                  src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/tradeoff//home.png"
                  alt="Chart"
                  className="w-4 h-4 opacity-60 group-hover:opacity-100 transition-opacity"
                  style={{ filter: 'brightness(0) saturate(100%) invert(100%)' }}
                />
                <span className="text-sm">Chart</span>
              </button>
              <button
                onClick={() => {
                  // Go back to competitions dashboard
                  onBack();
                }}
                className="flex items-center gap-2 text-white/60 hover:text-white transition-colors group"
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
              >
                <img
                  src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/tradeoff//Globe%20(1).png"
                  alt="Competitions"
                  className="w-4 h-4 opacity-60 group-hover:opacity-100 transition-opacity"
                  style={{ filter: 'brightness(0) saturate(100%) invert(100%)' }}
                />
                <span className="text-sm">Competitions</span>
              </button>
              <button
                className="flex items-center gap-2 text-white font-medium hover:text-white/80 transition-colors"
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
              >
                <img
                  src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/tradeoff//Bar%20Chart%201.png"
                  alt="Analytics"
                  className="w-4 h-4 opacity-100"
                  style={{ filter: 'brightness(0) saturate(100%) invert(100%)' }}
                />
                <span className="text-sm">Analytics</span>
              </button>
            </div>

            {/* Create Competition Button - Turquoise Highlighted */}
            <button
              className="flex items-center gap-3 bg-teal-500/[0.15] hover:bg-teal-500/[0.25] border border-teal-500/[0.4] hover:border-teal-500/[0.6] text-teal-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-[inset_0_1px_0_rgba(20,184,166,0.15)] hover:shadow-[inset_0_1px_0_rgba(20,184,166,0.25)] backdrop-blur-sm active:scale-[0.98]"
              style={{
                fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                boxShadow: '0 0 20px rgba(20, 184, 166, 0.15), inset 0 1px 0 rgba(20, 184, 166, 0.15)'
              }}
            >
              <span>Create Competition</span>
              <div className="flex items-center gap-1">
                <kbd className="px-2 py-1 text-xs font-semibold text-teal-300/80 bg-teal-500/[0.1] border border-teal-500/[0.3] rounded shadow-sm min-w-[20px] h-[20px] flex items-center justify-center">
                  ⌘
                </kbd>
                <kbd className="px-2 py-1 text-xs font-semibold text-teal-300/80 bg-teal-500/[0.1] border border-teal-500/[0.3] rounded shadow-sm min-w-[20px] h-[20px] flex items-center justify-center">
                  K
                </kbd>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8 relative z-10">
        {/* Page Title */}
        <div className="mb-8">
          <h1 className="text-5xl font-light text-white mb-6" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
            Competition Analytics
          </h1>
          <p className="text-white/60 text-lg">
            Track your competition performance and revenue
          </p>
        </div>

        {/* Revenue Cards - Clean Style */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Total Revenue */}
          <Card className="bg-white/[0.02] border-white/[0.08] hover:border-white/[0.12] transition-all duration-200 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_40px_rgba(0,0,0,0.4)]">
            <CardContent className="p-6">
              <div className="mb-4">
                <div className="text-3xl font-bold text-white mb-1" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                  ${totalRevenue.toLocaleString()}
                </div>
                <div className="text-sm font-medium text-white/60 tracking-wide">Total Revenue</div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-sm text-green-400 font-medium">+12.5%</span>
                <span className="text-sm text-white/50">vs last month</span>
              </div>
            </CardContent>
          </Card>

          {/* Total Participants */}
          <Card className="bg-white/[0.02] border-white/[0.08] hover:border-white/[0.12] transition-all duration-200 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_40px_rgba(0,0,0,0.4)]">
            <CardContent className="p-6">
              <div className="mb-4">
                <div className="text-3xl font-bold text-white mb-1" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                  {totalParticipants.toLocaleString()}
                </div>
                <div className="text-sm font-medium text-white/60 tracking-wide">Total Participants</div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <span className="text-sm text-blue-400 font-medium">+8.2%</span>
                <span className="text-sm text-white/50">vs last month</span>
              </div>
            </CardContent>
          </Card>

          {/* Active Competitions */}
          <Card className="bg-white/[0.02] border-white/[0.08] hover:border-white/[0.12] transition-all duration-200 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_40px_rgba(0,0,0,0.4)]">
            <CardContent className="p-6">
              <div className="mb-4">
                <div className="text-3xl font-bold text-white mb-1" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                  {activeCompetitions}
                </div>
                <div className="text-sm font-medium text-white/60 tracking-wide">Active Competitions</div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                <span className="text-sm text-yellow-400 font-medium">Live now</span>
              </div>
            </CardContent>
          </Card>

          {/* Completed Competitions */}
          <Card className="bg-white/[0.02] border-white/[0.08] hover:border-white/[0.12] transition-all duration-200 shadow-[0_8px_32px_rgba(0,0,0,0.3)] hover:shadow-[0_12px_40px_rgba(0,0,0,0.4)]">
            <CardContent className="p-6">
              <div className="mb-4">
                <div className="text-3xl font-bold text-white mb-1" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                  {completedCompetitions}
                </div>
                <div className="text-sm font-medium text-white/60 tracking-wide">Completed</div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span className="text-sm text-gray-400 font-medium">All time</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Revenue Graph */}
        <Card className="bg-white/[0.02] border-white/[0.08] mb-8 shadow-[0_8px_32px_rgba(0,0,0,0.3)]">
          <CardHeader className="pb-4">
            <CardTitle className="text-white text-xl font-semibold" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
              Revenue Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="h-64 flex items-end justify-between gap-2">
              {/* Simple bar chart with sample data */}
              {[120, 180, 240, 320, 280, 420, 380, 520, 480, 640, 720, 850].map((value, index) => (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div
                    className="w-full bg-gradient-to-t from-green-500/60 to-green-400/80 rounded-t-sm transition-all duration-500 hover:from-green-500/80 hover:to-green-400/100"
                    style={{ height: `${(value / 850) * 100}%` }}
                  ></div>
                  <div className="text-xs text-white/50 mt-2">
                    {['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][index]}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Competitions with Leaderboards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* My Competitions */}
          <Card className="bg-white/[0.02] border-white/[0.08] shadow-[0_8px_32px_rgba(0,0,0,0.3)]">
            <CardHeader className="pb-4">
              <CardTitle className="text-white text-xl font-semibold flex items-center gap-3" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                <Trophy className="w-6 h-6 text-yellow-400" />
                My Competitions
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="space-y-0">
                {myCreatedCompetitions.length > 0 ? (
                  myCreatedCompetitions.map((competition, index) => (
                    <div
                      key={competition.id}
                      className={`p-6 hover:bg-white/[0.02] transition-all duration-200 cursor-pointer ${
                        index !== myCreatedCompetitions.length - 1 ? 'border-b border-white/[0.08]' : ''
                      }`}
                      onClick={() => setSelectedCompetition(competition)}
                    >
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-white font-semibold text-lg" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                              {competition.name}
                            </h3>
                            <Badge className={`${getStatusColor(competition.status)} border text-xs font-medium`}>
                              {competition.status}
                            </Badge>
                          </div>
                          <p className="text-white/60 text-sm mb-3">
                            {competition.description || 'No description available'}
                          </p>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-green-400" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                            ${((competition.entry_fee || 0) * (competition.participant_count || 0)).toLocaleString()}
                          </div>
                          <div className="text-sm text-white/60 font-medium">Revenue</div>
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div className="text-center">
                          <div className="text-white font-semibold">{competition.participant_count || 0}</div>
                          <div className="text-white/60">Participants</div>
                        </div>
                        <div className="text-center">
                          <div className="text-white font-semibold">${(competition.prize_pool || 0).toLocaleString()}</div>
                          <div className="text-white/60">Prize Pool</div>
                        </div>
                        <div className="text-center">
                          <div className="text-white font-semibold">
                            {new Date(competition.competition_start).toLocaleDateString()}
                          </div>
                          <div className="text-white/60">Start Date</div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="p-12 text-center">
                    <Trophy className="w-12 h-12 text-white/20 mx-auto mb-4" />
                    <h3 className="text-white/60 text-lg mb-2" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                      No competitions yet
                    </h3>
                    <p className="text-white/40">Create your first competition to start tracking analytics</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Sample Leaderboard */}
          <Card className="bg-white/[0.02] border-white/[0.08] shadow-[0_8px_32px_rgba(0,0,0,0.3)]">
            <CardHeader className="pb-4">
              <CardTitle className="text-white text-xl font-semibold flex items-center gap-3" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                <Crown className="w-6 h-6 text-yellow-400" />
                Top Performers
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                {[
                  { name: 'Alex Chen', profit: '+$12,450', rank: 1, avatar: '🏆' },
                  { name: 'Sarah Kim', profit: '+$8,920', rank: 2, avatar: '🥈' },
                  { name: 'Mike Johnson', profit: '+$7,340', rank: 3, avatar: '🥉' },
                  { name: 'Emma Davis', profit: '+$5,680', rank: 4, avatar: '👤' },
                  { name: 'David Wilson', profit: '+$4,230', rank: 5, avatar: '👤' }
                ].map((trader, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-4 bg-white/[0.02] rounded-lg border border-white/[0.05] hover:border-white/[0.1] transition-all duration-200 shadow-[inset_0_1px_0_rgba(255,255,255,0.05)]"
                  >
                    <div className="flex items-center gap-4">
                      <div className="w-10 h-10 bg-white/[0.08] rounded-full flex items-center justify-center text-lg font-bold border border-white/[0.1]">
                        {trader.rank <= 3 ? trader.avatar : trader.rank}
                      </div>
                      <div>
                        <div className="text-white font-semibold" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                          {trader.name}
                        </div>
                        <div className="text-white/60 text-sm">Rank #{trader.rank}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-green-400 font-bold text-lg" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                        {trader.profit}
                      </div>
                      <div className="text-white/60 text-sm">Profit</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </motion.div>
  );
};

export default CompetitionAnalytics;
