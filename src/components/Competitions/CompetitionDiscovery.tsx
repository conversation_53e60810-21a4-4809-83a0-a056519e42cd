import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Trophy, Users, Clock, DollarSign, Calendar, Target, Award, Timer, Globe, Crown, TrendingUp, TrendingDown, Search, Filter, SortDesc, Plus } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useCompetitions, Competition, LeaderboardEntry } from '@/hooks/useCompetitions';
import { useToast } from '@/components/ui/use-toast';
import { formatDistanceToNow, isAfter, isBefore } from 'date-fns';
import CompetitionCountdown from './CompetitionCountdown';
import CompetitionCreatorHub from './CompetitionCreatorHub';
import CompetitionCreatorDashboard from './CompetitionCreatorDashboard';
import CompetitionAnalytics from './CompetitionAnalytics';
import { OFFICIAL_OSIS_BUSINESS_ID, OFFICIAL_OSIS_HANDLE } from '@/types/whopCompetition';
import { useAuth } from '@/contexts/AuthContext';

interface CompetitionDiscoveryProps {
  onCompetitionSelect?: (competition: Competition) => void;
}



const CompetitionDiscovery: React.FC<CompetitionDiscoveryProps> = ({
  onCompetitionSelect
}) => {
  const { competitions, userCompetitions, loading, joinCompetition, refreshCompetitions } = useCompetitions();
  const { user } = useAuth();
  const { toast } = useToast();
  const [filter, setFilter] = useState<'all' | 'open' | 'active' | 'completed'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'newest' | 'participants' | 'prize'>('newest');
  const [showCreatorHub, setShowCreatorHub] = useState(false);
  const [selectedCreatorCompetition, setSelectedCreatorCompetition] = useState<Competition | null>(null);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [viewMode, setViewMode] = useState<'discover' | 'my-competitions' | 'creator-dashboard'>('discover');

  // Fetch competitions on mount
  useEffect(() => {
    refreshCompetitions();
  }, [refreshCompetitions]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'active': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'completed': return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getCompetitionScopeBadge = (competition: Competition) => {
    if (!competition.competition_scope || competition.competition_scope === 'public') {
      return (
        <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
          <Globe className="w-3 h-3 mr-1" />
          Public
        </Badge>
      );
    }

    if (competition.competition_scope === 'whop_local') {
      return (
        <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
          <Users className="w-3 h-3 mr-1" />
          Community
        </Badge>
      );
    }

    if (competition.competition_scope === 'whop_cross_community') {
      const isOfficialOsis = competition.whop_business_id === OFFICIAL_OSIS_BUSINESS_ID ||
                            competition.whop_business_handle === OFFICIAL_OSIS_HANDLE;
      return (
        <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
          <Crown className="w-3 h-3 mr-1" />
          {isOfficialOsis ? 'Official Osis' : 'Cross-Community'}
        </Badge>
      );
    }

    return null;
  };

  const getTimeRemaining = (competition: Competition) => {
    const now = new Date();
    const start = new Date(competition.competition_start);
    const end = new Date(competition.competition_end);

    if (isBefore(now, start)) {
      return {
        label: 'Starts in',
        time: formatDistanceToNow(start),
        urgent: false
      };
    } else if (isAfter(now, start) && isBefore(now, end)) {
      const remaining = formatDistanceToNow(end);
      return {
        label: 'Ends in',
        time: remaining,
        urgent: remaining.includes('hour') || remaining.includes('minute')
      };
    } else {
      return {
        label: 'Ended',
        time: formatDistanceToNow(end) + ' ago',
        urgent: false
      };
    }
  };

  const isUserParticipating = (competitionId: string) => {
    return userCompetitions.some(uc => uc.competition_id === competitionId);
  };

  const handleJoinCompetition = async (competitionId: string) => {
    try {
      await joinCompetition(competitionId);
    } catch (error) {
      // Error is already handled in the hook
    }
  };

  // Enhanced mock competitions with rich sample data
  const mockCompetitions: Competition[] = [
    {
      id: 'mock-1',
      name: 'Weekly Crypto Challenge',
      description: 'Trade the top 10 cryptocurrencies and compete for the highest returns this week.',
      creator_id: user?.id || 'mock-user',
      starting_balance: 100000,
      entry_fee: 50,
      prize_pool: 5000,
      status: 'active' as const,
      competition_start: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      competition_end: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      participant_count: 127
    },
    {
      id: 'mock-2',
      name: 'S&P 500 Masters',
      description: 'Focus on large-cap stocks from the S&P 500. Show your stock picking skills.',
      creator_id: 'other-user-1',
      starting_balance: 100000,
      entry_fee: 100,
      prize_pool: 10000,
      status: 'open' as const,
      competition_start: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      competition_end: new Date(Date.now() + 31 * 24 * 60 * 60 * 1000).toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      participant_count: 89
    },
    {
      id: 'mock-3',
      name: 'Forex Fury',
      description: 'Currency pairs trading competition. EUR/USD, GBP/USD, and more major pairs.',
      creator_id: 'other-user-2',
      starting_balance: 50000,
      entry_fee: 75,
      prize_pool: 7500,
      status: 'active' as const,
      competition_start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      competition_end: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      participant_count: 156
    },
    {
      id: 'mock-4',
      name: 'Options Olympiad',
      description: 'Advanced options strategies competition. Calls, puts, spreads, and complex strategies.',
      creator_id: 'other-user-3',
      starting_balance: 100000,
      entry_fee: 200,
      prize_pool: 15000,
      status: 'open' as const,
      competition_start: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
      competition_end: new Date(Date.now() + 24 * 24 * 60 * 60 * 1000).toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      participant_count: 67
    }
  ];

  const allCompetitionsWithMock = [...competitions, ...mockCompetitions];

  // Get user's created competitions (including mock data for testing)
  const myCreatedCompetitions = allCompetitionsWithMock.filter(competition =>
    competition.creator_id === user?.id
  );

  const filteredCompetitions = (viewMode === 'my-competitions'
    ? allCompetitionsWithMock.filter(comp => comp.creator_id === user?.id)
    : allCompetitionsWithMock
  ).filter(competition => {
    // Status filter
    if (filter !== 'all' && competition.status !== filter) return false;

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        competition.name.toLowerCase().includes(query) ||
        competition.description?.toLowerCase().includes(query)
      );
    }

    return true;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'participants':
        return (b.participant_count || 0) - (a.participant_count || 0);
      case 'prize':
        return (b.starting_balance || 0) - (a.starting_balance || 0);
      case 'newest':
      default:
        return new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime();
    }
  });

  // Debug logging
  console.log('🔥 COMPETITION DISCOVERY LOADED 🔥');
  console.log('All competitions:', competitions);
  console.log('Filtered competitions:', filteredCompetitions);
  console.log('Mock competitions:', mockCompetitions);
  console.log('View mode:', viewMode);
  console.log('User ID:', user?.id);
  console.log('Loading:', loading);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"></div>
      </div>
    );
  }

  // Show analytics view
  if (showAnalytics) {
    return (
      <CompetitionAnalytics
        onBack={() => setShowAnalytics(false)}
      />
    );
  }

  // Show creator dashboard if selected
  if (viewMode === 'creator-dashboard' && selectedCreatorCompetition) {
    return (
      <div className="min-h-screen bg-[#0A0A0A] p-6">
        <div className="max-w-7xl mx-auto">
          <CompetitionCreatorDashboard
            competition={selectedCreatorCompetition}
            onBack={() => {
              setViewMode('my-competitions');
              setSelectedCreatorCompetition(null);
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -30 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className="min-h-screen bg-[#0A0A0A] text-white"
    >
      {/* Simple Top Navbar */}
      <div className="w-full bg-[#0A0A0A] border-b border-white/[0.08] px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <img
              src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/tradeoff//ChatGPT%20Image%20Jul%2023,%202025,%2008_29_13%20PM.png"
              alt="TradeOff Logo"
              className="w-10 h-10"
            />
          </div>

          <div className="flex items-center gap-8">
            {/* Navigation - Top Right */}
            <div className="flex items-center gap-6">
              <button
                className="flex items-center gap-2 text-white/60 hover:text-white transition-colors"
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
              >
                <img
                  src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/tradeoff//home.png"
                  alt="Chart"
                  className="w-4 h-4 opacity-60"
                  style={{ filter: 'brightness(0) saturate(100%) invert(100%)' }}
                />
                <span className="text-sm">Chart</span>
              </button>
              <button
                className="flex items-center gap-2 text-white font-medium hover:text-white/80 transition-colors"
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
              >
                <img
                  src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/tradeoff//Globe%20(1).png"
                  alt="Competitions"
                  className="w-4 h-4 opacity-70"
                  style={{ filter: 'brightness(0) saturate(100%) invert(100%)' }}
                />
                <span className="text-sm">Competitions</span>
              </button>
              <button
                onClick={() => setShowAnalytics(true)}
                className="flex items-center gap-2 text-white/60 hover:text-white transition-colors"
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
              >
                <img
                  src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/tradeoff//Bar%20Chart%201.png"
                  alt="Analytics"
                  className="w-4 h-4 opacity-60"
                  style={{ filter: 'brightness(0) saturate(100%) invert(100%)' }}
                />
                <span className="text-sm">Analytics</span>
              </button>
            </div>

            {/* Create Competition Button - Turquoise Highlighted */}
            <button
              onClick={() => setShowCreatorHub(true)}
              className="flex items-center gap-3 bg-teal-500/[0.15] hover:bg-teal-500/[0.25] border border-teal-500/[0.4] hover:border-teal-500/[0.6] text-teal-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-[inset_0_1px_0_rgba(20,184,166,0.15)] hover:shadow-[inset_0_1px_0_rgba(20,184,166,0.25)] backdrop-blur-sm active:scale-[0.98]"
              style={{
                fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif',
                boxShadow: '0 0 20px rgba(20, 184, 166, 0.15), inset 0 1px 0 rgba(20, 184, 166, 0.15)'
              }}
            >
              <span>Create Competition</span>
              <div className="flex items-center gap-1">
                <kbd className="px-2 py-1 text-xs font-semibold text-teal-300/80 bg-teal-500/[0.1] border border-teal-500/[0.3] rounded shadow-sm min-w-[20px] h-[20px] flex items-center justify-center">
                  ⌘
                </kbd>
                <kbd className="px-2 py-1 text-xs font-semibold text-teal-300/80 bg-teal-500/[0.1] border border-teal-500/[0.3] rounded shadow-sm min-w-[20px] h-[20px] flex items-center justify-center">
                  K
                </kbd>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Subtle background gradient */}
      <div
        className="absolute inset-0 pointer-events-none opacity-30 top-16"
        style={{
          background: 'linear-gradient(135deg, rgba(255,255,255,0.008) 0%, rgba(255,255,255,0.003) 50%, transparent 100%)'
        }}
      />

      {/* Content Container with Proper Spacing */}
      <div className="max-w-7xl mx-auto px-8 py-16 relative z-10">

        {/* Hero Section with Better Spacing */}
        <div className="text-center mb-24 mt-20">
          <h1 className="text-5xl font-light text-white tracking-tight mb-8" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
            {viewMode === 'my-competitions' ? 'My Competitions' : 'Discover Trading Competitions'}
          </h1>
          <p className="text-white/70 text-xl max-w-3xl mx-auto leading-relaxed">
            {viewMode === 'my-competitions'
              ? 'Manage and track your competitions with detailed analytics and performance insights'
              : 'Join live trading competitions or create your own. Compete with traders worldwide and showcase your skills'
            }
          </p>
        </div>

        {/* View Mode Tabs with Better Spacing */}
        <div className="flex justify-center mb-16">
          <div className="flex items-center gap-1 bg-white/[0.05] border border-white/[0.08] rounded-xl p-1.5">
            <button
              onClick={() => setViewMode('discover')}
              className={`px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 ${
                viewMode === 'discover'
                  ? 'bg-white/[0.1] text-white shadow-[inset_0_1px_0_rgba(255,255,255,0.1)]'
                  : 'text-white/70 hover:text-white hover:bg-white/[0.05]'
              }`}
              style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
            >
              Discover
            </button>
            <button
              onClick={() => setViewMode('my-competitions')}
              className={`px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 ${
                viewMode === 'my-competitions'
                  ? 'bg-white/[0.1] text-white shadow-[inset_0_1px_0_rgba(255,255,255,0.1)]'
                  : 'text-white/70 hover:text-white hover:bg-white/[0.05]'
              }`}
              style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
            >
              My Competitions ({myCreatedCompetitions.length})
            </button>
          </div>
        </div>

      {/* Search Bar */}
      <div className="w-full max-w-md mb-8">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <img
              src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons//Tradingview%20Financial%20Chart.png"
              alt="Search"
              className="w-4 h-4 opacity-60"
            />
          </div>
          <input
            type="text"
            placeholder="Search for anything..."
            className="w-full pl-12 pr-4 py-3 bg-[#2A2A2A] border border-white/20 rounded-full text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 shadow-lg"
            style={{
              boxShadow: '0 0 20px rgba(255, 255, 255, 0.1), inset 0 1px 3px rgba(255, 255, 255, 0.1)'
            }}
          />
        </div>
      </div>

      {/* Filter Toggles */}
      <div className="flex items-center gap-6 mb-16">
        {/* Status Filter */}
        <div className="flex items-center gap-2">
          <span className="text-white/60 text-sm">Status:</span>
          <div className="flex gap-1">
            {['all', 'open', 'active', 'completed'].map((status) => (
              <button
                key={status}
                onClick={() => setFilter(status as any)}
                className={`px-3 py-1 text-xs rounded-full transition-all duration-200 ${
                  filter === status
                    ? 'bg-white/20 text-white border border-white/30'
                    : 'bg-white/5 text-white/60 border border-white/10 hover:bg-white/10 hover:text-white/80'
                }`}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Sort Filter */}
        <div className="flex items-center gap-2">
          <span className="text-white/60 text-sm">Sort:</span>
          <div className="flex gap-1">
            {[
              { key: 'newest', label: 'Newest' },
              { key: 'participants', label: 'Popular' },
              { key: 'prize', label: 'Prize' }
            ].map((option) => (
              <button
                key={option.key}
                className={`px-3 py-1 text-xs rounded-full transition-all duration-200 ${
                  'bg-white/5 text-white/60 border border-white/10 hover:bg-white/10 hover:text-white/80'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Competition Grid with Smooth Transitions */}
      <motion.div
        key={viewMode}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.4, ease: "easeInOut" }}
        className="flex-1 w-full max-w-6xl mx-auto px-8"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8 justify-center">
          {/* Always show 4 cards - fill with real competitions first, then placeholders */}
          {Array.from({ length: 4 }).map((_, index) => {
            const competition = filteredCompetitions[index];

            if (competition) {
              // Show real competition data
              return (
                <motion.div
                  key={competition.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="bg-white/[0.02] border border-white/[0.08] rounded-xl p-6 hover:border-white/[0.15] hover:bg-white/[0.04] transition-all duration-200 cursor-pointer group"
                  onClick={() => {
                    if (viewMode === 'my-competitions' && competition.creator_id === user?.id) {
                      setSelectedCreatorCompetition(competition);
                      setViewMode('creator-dashboard');
                    } else {
                      onCompetitionSelect?.(competition);
                    }
                  }}
                >
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="text-white font-semibold text-lg line-clamp-1" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                          {competition.name}
                        </h3>
                        <span className={`px-2 py-1 rounded-lg text-xs font-medium ${getStatusColor(competition.status)}`}>
                          {competition.status}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-teal-400 text-xs font-medium px-2 py-1 bg-teal-500/[0.1] rounded-md">
                          {competition.id.includes('crypto') ? 'Crypto Trading' :
                           competition.id.includes('forex') ? 'Forex Trading' :
                           competition.id.includes('options') ? 'Options Trading' : 'Stock Trading'}
                        </span>
                        <span className="text-white/50 text-xs">
                          {competition.id.includes('mock-1') ? '7 days' :
                           competition.id.includes('mock-2') ? '30 days' :
                           competition.id.includes('mock-3') ? '14 days' : '21 days'}
                        </span>
                      </div>
                      <p className="text-white/60 text-sm line-clamp-2">
                        {competition.description || 'No description available'}
                      </p>
                    </div>
                  </div>

                  {/* Prize & Entry Info */}
                  <div className="grid grid-cols-2 gap-3 mb-4">
                    <div className="bg-white/[0.03] rounded-lg p-3 border border-white/[0.05]">
                      <div className="text-white/60 text-xs mb-1">Prize Pool</div>
                      <div className="text-green-400 font-bold text-lg">${(competition.prize_pool || 0).toLocaleString()}</div>
                    </div>
                    <div className="bg-white/[0.03] rounded-lg p-3 border border-white/[0.05]">
                      <div className="text-white/60 text-xs mb-1">Entry Fee</div>
                      <div className="text-white font-bold text-lg">${(competition.entry_fee || 0).toLocaleString()}</div>
                    </div>
                  </div>

                  {/* Participants & Mini Leaderboard */}
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-white/60 text-sm">Participants</span>
                      <span className="text-white font-medium">{competition.participant_count || 0}</span>
                    </div>

                    {/* Mini Leaderboard */}
                    {competition.status === 'active' && (
                      <div className="bg-white/[0.02] rounded-lg p-3 border border-white/[0.05]">
                        <div className="text-white/60 text-xs mb-2 flex items-center gap-1">
                          <Trophy className="w-3 h-3" />
                          Top Performers
                        </div>
                        <div className="space-y-1">
                          {[
                            { name: competition.id.includes('mock-1') ? 'CryptoKing' :
                                    competition.id.includes('mock-2') ? 'WallStreetWolf' :
                                    competition.id.includes('mock-3') ? 'ForexPhantom' : 'OptionsOracle',
                              return: competition.id.includes('mock-1') ? '+24.5%' :
                                     competition.id.includes('mock-2') ? '+31.2%' :
                                     competition.id.includes('mock-3') ? '+19.8%' : '+42.1%' },
                            { name: competition.id.includes('mock-1') ? 'BlockchainBull' :
                                    competition.id.includes('mock-2') ? 'ValueInvestor' :
                                    competition.id.includes('mock-3') ? 'CurrencyKing' : 'VolatilityViper',
                              return: competition.id.includes('mock-1') ? '+18.2%' :
                                     competition.id.includes('mock-2') ? '+28.9%' :
                                     competition.id.includes('mock-3') ? '+17.3%' : '+38.7%' },
                            { name: competition.id.includes('mock-1') ? 'DeFiDegen' :
                                    competition.id.includes('mock-2') ? 'TechTrader' :
                                    competition.id.includes('mock-3') ? 'PipMaster' : 'GreekGuru',
                              return: competition.id.includes('mock-1') ? '+15.7%' :
                                     competition.id.includes('mock-2') ? '+26.4%' :
                                     competition.id.includes('mock-3') ? '+14.9%' : '+35.2%' }
                          ].map((trader, idx) => (
                            <div key={idx} className="flex items-center justify-between text-xs">
                              <div className="flex items-center gap-2">
                                <span className="text-white/40 w-3">{idx + 1}.</span>
                                <span className="text-white/80">{trader.name}</span>
                              </div>
                              <span className="text-green-400 font-medium">{trader.return}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Action Button */}
                  {competition.creator_id === user?.id ? (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedCreatorCompetition(competition);
                        setViewMode('creator-dashboard');
                      }}
                      className="w-full bg-teal-500/[0.15] hover:bg-teal-500/[0.25] border border-teal-500/[0.3] hover:border-teal-500/[0.5] text-teal-400 py-3 rounded-lg text-sm font-medium transition-all duration-200"
                      style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                    >
                      <Trophy className="w-4 h-4 mr-2 inline" />
                      Manage Competition
                    </button>
                  ) : (
                    <button
                      className="w-full bg-teal-500/[0.08] hover:bg-teal-500/[0.15] border border-teal-500/[0.2] hover:border-teal-500/[0.4] text-teal-400 py-3 rounded-lg text-sm font-medium transition-all duration-200"
                      style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                    >
                      Join Competition
                    </button>
                  )}
                </motion.div>
              );
            } else {
              // Show placeholder card
              return (
                <motion.div
                  key={`empty-${index}`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 0.5, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="bg-white/[0.02] border border-white/[0.08] rounded-xl p-6"
                >
                  <div className="space-y-4">
                    <div className="h-4 bg-white/[0.05] rounded"></div>
                    <div className="h-3 bg-white/[0.05] rounded w-3/4"></div>
                    <div className="space-y-2">
                      <div className="h-2 bg-white/[0.05] rounded"></div>
                      <div className="h-2 bg-white/[0.05] rounded"></div>
                      <div className="h-2 bg-white/[0.05] rounded"></div>
                    </div>
                    <div className="h-8 bg-white/[0.05] rounded"></div>
                  </div>
                </motion.div>
              );
            }
          })}
        </div>

        {filteredCompetitions.length > 4 && (
          <div className="text-center mt-8">
            <button className="text-white/60 text-sm hover:text-white/80 transition-colors">
              Show more competitions
            </button>
          </div>
        )}
      </motion.div>

      {/* Competition Creator Hub Modal */}
      {showCreatorHub && (
        <CompetitionCreatorHub
          isOpen={showCreatorHub}
          onClose={() => setShowCreatorHub(false)}
          onCompetitionCreated={() => {
            setShowCreatorHub(false);
            refreshCompetitions();
            // Switch to "My Competitions" view to show the new competition
            setTimeout(() => {
              setViewMode('my-competitions');
            }, 500);
          }}
        />
      )}
      </div>
    </motion.div>
  );
};

export default CompetitionDiscovery;
