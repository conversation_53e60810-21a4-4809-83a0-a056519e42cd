import React, { useState, useEffect } from 'react';
import { X, Upload, Tag, FileText, Folder, DollarSign } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { publishAgent, type AgentCategory } from '@/services/discoverService';
import { getAgents, type Agent } from '@/services/agentService';
import { updateAgentPricing } from '@/services/marketplaceService';
import { validateAgentPublication } from '@/services/contentFilterService';

interface PublishAgentModalProps {
  isOpen: boolean;
  onClose: () => void;
  categories: AgentCategory[];
  onSuccess: () => void;
}

const PublishAgentModal: React.FC<PublishAgentModalProps> = ({
  isOpen,
  onClose,
  categories,
  onSuccess
}) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [loadingAgents, setLoadingAgents] = useState(false);
  const [userAgents, setUserAgents] = useState<Agent[]>([]);
  
  const [formData, setFormData] = useState({
    agentId: '',
    name: '',
    description: '',
    category: '',
    tags: [] as string[],
    isPaid: false,
    price: ''
  });

  const [newTag, setNewTag] = useState('');

  // Common tags for suggestions
  const suggestedTags = [
    'RSI', 'MACD', 'Moving Average', 'Bollinger Bands', 'Support/Resistance',
    'Breakout', 'Momentum', 'Volume', 'Candlestick', 'Trend Following',
    'Mean Reversion', 'Scalping', 'Position Sizing', 'Stop Loss', 'Take Profit',
    'Day Trading', 'Swing Trading', 'Options', 'Risk Management'
  ];

  // Load user's agents when modal opens
  useEffect(() => {
    if (isOpen) {
      loadUserAgents();
      console.log('PublishAgentModal opened, categories available:', categories.length);
    }
  }, [isOpen, categories]);

  const loadUserAgents = async () => {
    try {
      setLoadingAgents(true);
      const agents = await getAgents();

      // SECURITY: Only show owned agents (not purchased ones) for publishing
      const ownedAgents = agents.filter(agent => agent.is_owned === true);

      console.log(`Loaded ${agents.length} total agents, ${ownedAgents.length} owned agents for publishing`);
      setUserAgents(ownedAgents);
    } catch (error) {
      console.error('Error loading user agents:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load your agents"
      });
    } finally {
      setLoadingAgents(false);
    }
  };

  const handleAgentSelect = (agentId: string) => {
    const selectedAgent = userAgents.find(agent => agent.id === agentId);
    if (selectedAgent) {
      setFormData(prev => ({
        ...prev,
        agentId,
        name: selectedAgent.name,
        description: selectedAgent.description || ''
      }));
    }
  };

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSuggestedTagClick = (tag: string) => {
    if (!formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.agentId || !formData.name || !formData.category) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please fill in all required fields"
      });
      return;
    }

    // SECURITY: Validate that the selected agent is owned (not purchased)
    const selectedAgent = userAgents.find(agent => agent.id === formData.agentId);
    if (!selectedAgent || !selectedAgent.is_owned) {
      toast({
        variant: "destructive",
        title: "Security Error",
        description: "You can only publish agents you created, not purchased agents."
      });
      return;
    }

    // Validate price if paid
    if (formData.isPaid && (!formData.price || parseFloat(formData.price) <= 0)) {
      toast({
        variant: "destructive",
        title: "Invalid Price",
        description: "Please enter a valid price greater than $0"
      });
      return;
    }

    try {
      setLoading(true);

      // Validate content and check for bans
      const validation = await validateAgentPublication(
        formData.name,
        formData.description,
        formData.isPaid ? parseFloat(formData.price) : undefined
      );

      if (!validation.canPublish) {
        toast({
          variant: "destructive",
          title: "Publication Blocked",
          description: validation.errors.join('. ')
        });
        return;
      }

      // Show warnings if any
      if (validation.warnings.length > 0) {
        const proceed = confirm(
          `Warning: ${validation.warnings.join('. ')}\n\nDo you want to proceed with publication?`
        );
        if (!proceed) {
          return;
        }
      }

      // First publish the agent
      const response = await publishAgent({
        agentId: formData.agentId,
        name: formData.name,
        description: formData.description,
        category: formData.category,
        tags: formData.tags
      });

      if (response.success) {
        // If successful and user wants to list for sale, update pricing
        if (formData.isPaid || formData.price) {
          const priceValue = formData.isPaid ? parseFloat(formData.price) : 0;

          const pricingResponse = await updateAgentPricing(formData.agentId, {
            price: formData.isPaid ? priceValue : null,
            is_for_sale: true // Publishing automatically lists for sale
          });

          if (!pricingResponse.success) {
            console.warn('Failed to set pricing:', pricingResponse.error);
            // Don't fail the whole operation, just warn
            toast({
              title: "Partially Successful",
              description: "Agent published but pricing could not be set. You can set pricing later."
            });
          }
        }

        toast({
          title: "Success",
          description: formData.isPaid
            ? `Agent published and listed for sale at $${parseFloat(formData.price).toFixed(2)}!`
            : "Agent published successfully!"
        });
        onSuccess();
        handleClose();
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: response.error || "Failed to publish agent"
        });
      }
    } catch (error) {
      console.error('Error publishing agent:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({
      agentId: '',
      name: '',
      description: '',
      category: '',
      tags: [],
      isPaid: false,
      price: ''
    });
    setNewTag('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md bg-gradient-to-br from-[#0D0D0D] via-[#111111] to-[#0A0A0A] border border-white/[0.12] text-white max-h-[85vh] overflow-y-auto rounded-2xl shadow-[0_20px_60px_rgba(0,0,0,0.6),inset_0_1px_0_rgba(255,255,255,0.1)] backdrop-blur-md">
        <DialogHeader>
          <DialogTitle className="text-base font-medium flex items-center gap-2">
            <Upload className="w-4 h-4" />
            Publish Agent
          </DialogTitle>
          <DialogDescription className="text-white/60 text-sm">
            Share your agent with the community
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 py-2">
          {/* Agent Selection */}
          <div className="space-y-1">
            <label className="text-xs font-medium text-white/60">
              Agent *
            </label>
            <Select value={formData.agentId} onValueChange={handleAgentSelect}>
              <SelectTrigger className="bg-gradient-to-br from-white/[0.04] to-white/[0.02] border border-white/[0.12] text-white h-9 text-sm rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] transition-all duration-200 focus:outline-none focus:ring-0">
                <SelectValue placeholder="Choose agent" />
              </SelectTrigger>
              <SelectContent className="bg-[#1A1A1A] border-white/[0.08] text-white">
                {loadingAgents ? (
                  <SelectItem value="loading" disabled>Loading...</SelectItem>
                ) : userAgents.length === 0 ? (
                  <SelectItem value="no-agents" disabled>No owned agents available</SelectItem>
                ) : (
                  userAgents.map((agent) => (
                    <SelectItem key={agent.id!} value={agent.id!} className="text-white hover:bg-white/[0.04]">
                      {agent.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Name */}
          <div className="space-y-1">
            <label className="text-xs font-medium text-white/60">
              Name *
            </label>
            <Input
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Display name"
              className="bg-gradient-to-br from-white/[0.04] to-white/[0.02] border border-white/[0.12] text-white placeholder:text-white/40 focus:border-white/[0.2] focus:bg-white/[0.06] focus:outline-none focus:ring-0 text-sm h-9 rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] transition-all duration-200"
            />
          </div>

          {/* Description */}
          <div className="space-y-1">
            <label className="text-xs font-medium text-white/60">
              Description
            </label>
            <Textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="What does your agent do?"
              rows={3}
              className="bg-gradient-to-br from-white/[0.04] to-white/[0.02] border border-white/[0.12] text-white placeholder:text-white/40 focus:border-white/[0.2] focus:bg-white/[0.06] focus:outline-none focus:ring-0 text-sm resize-none rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] transition-all duration-200"
            />
          </div>

          {/* Category */}
          <div className="space-y-1">
            <label className="text-xs font-medium text-white/60">
              Category * {categories.length === 0 && <span className="text-red-400">(No categories loaded)</span>}
              <button
                type="button"
                onClick={() => console.log('Categories:', categories)}
                className="ml-2 text-xs text-blue-400 hover:text-blue-300"
              >
                [Debug]
              </button>
            </label>
            <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
              <SelectTrigger className="bg-gradient-to-br from-white/[0.04] to-white/[0.02] border border-white/[0.12] text-white h-9 text-sm rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] transition-all duration-200 focus:outline-none focus:ring-0">
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent
                className="bg-gradient-to-br from-[#1A1A1A] to-[#151515] border border-white/[0.08] text-white shadow-[0_20px_60px_rgba(0,0,0,0.6)] rounded-lg backdrop-blur-md z-[99999]"
                position="popper"
                sideOffset={4}
                style={{ zIndex: 999999 }}
              >
                {categories.length === 0 ? (
                  <SelectItem value="no-categories" disabled className="text-white/50">
                    No categories available
                  </SelectItem>
                ) : (
                  categories.map((category) => (
                    <SelectItem key={category.id} value={category.name} className="text-white hover:bg-white/[0.04]">
                      {category.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Tags */}
          <div className="space-y-2">
            <label className="text-xs font-medium text-white/60">
              Tags
            </label>

            {/* Add Tag Input */}
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Add tag"
                className="bg-gradient-to-br from-white/[0.04] to-white/[0.02] border border-white/[0.12] text-white placeholder:text-white/40 focus:border-white/[0.2] focus:bg-white/[0.06] focus:outline-none focus:ring-0 text-sm h-8 rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] transition-all duration-200"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
              />
              <Button
                type="button"
                onClick={handleAddTag}
                variant="outline"
                className="bg-gradient-to-br from-white/[0.04] to-white/[0.02] border border-white/[0.12] text-white/70 hover:text-white hover:bg-white/[0.06] hover:border-white/[0.2] h-8 px-3 text-sm font-medium rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] transition-all duration-200"
              >
                Add
              </Button>
            </div>

            {/* Current Tags */}
            {formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {formData.tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="outline"
                    className="border-white/[0.12] text-white/80 bg-gradient-to-br from-white/[0.04] to-white/[0.02] hover:bg-white/[0.06] cursor-pointer h-7 px-3 text-xs font-medium rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] transition-all duration-200"
                    onClick={() => handleRemoveTag(tag)}
                  >
                    {tag}
                    <X className="w-3 h-3 ml-1.5" />
                  </Badge>
                ))}
              </div>
            )}

            {/* Suggested Tags */}
            <div className="space-y-2">
              <p className="text-xs text-white/50 font-medium">Suggested:</p>
              <div className="flex flex-wrap gap-2">
                {suggestedTags
                  .filter(tag => !formData.tags.includes(tag))
                  .slice(0, 8)
                  .map((tag) => (
                    <Badge
                      key={tag}
                      variant="outline"
                      className="border-white/[0.08] text-white/60 bg-gradient-to-br from-white/[0.02] to-white/[0.01] hover:border-white/[0.15] hover:text-white/80 hover:bg-white/[0.04] cursor-pointer text-xs h-6 px-2.5 rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.05)] transition-all duration-200"
                      onClick={() => handleSuggestedTagClick(tag)}
                    >
                      {tag}
                    </Badge>
                  ))}
              </div>
            </div>
          </div>

          {/* Pricing Options */}
          <div className="space-y-3 pt-2 border-t border-white/[0.06]">
            <label className="text-xs font-medium text-white/60">
              Marketplace Listing
            </label>

            {/* Free Option */}
            <div
              className={`p-4 border rounded-xl cursor-pointer transition-all duration-200 outline-none focus:outline-none ${
                !formData.isPaid
                  ? 'border-green-500/50 bg-gradient-to-br from-green-500/10 to-green-500/5 shadow-[inset_0_1px_0_rgba(34,197,94,0.2)]'
                  : 'border-white/[0.12] bg-gradient-to-br from-white/[0.04] to-white/[0.02] hover:border-white/[0.2] hover:bg-white/[0.06] shadow-[inset_0_1px_0_rgba(255,255,255,0.1)]'
              }`}
              onClick={() => setFormData(prev => ({ ...prev, isPaid: false, price: '' }))}
            >
              <div className="flex items-center gap-2">
                <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${
                  !formData.isPaid ? 'border-green-500 bg-green-500 shadow-[0_0_8px_rgba(34,197,94,0.4)]' : 'border-white/40'
                }`}>
                  {!formData.isPaid && <div className="w-2 h-2 bg-white rounded-full" />}
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-white">Free Agent</div>
                  <div className="text-xs text-white/60">
                    Share for free to build reputation
                  </div>
                </div>
              </div>
            </div>

            {/* Paid Option */}
            <div
              className={`p-4 border rounded-xl cursor-pointer transition-all duration-200 outline-none focus:outline-none ${
                formData.isPaid
                  ? 'border-green-500/50 bg-gradient-to-br from-green-500/10 to-green-500/5 shadow-[inset_0_1px_0_rgba(34,197,94,0.2)]'
                  : 'border-white/[0.12] bg-gradient-to-br from-white/[0.04] to-white/[0.02] hover:border-white/[0.2] hover:bg-white/[0.06] shadow-[inset_0_1px_0_rgba(255,255,255,0.1)]'
              }`}
              onClick={() => setFormData(prev => ({ ...prev, isPaid: true }))}
            >
              <div className="flex items-center gap-2">
                <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${
                  formData.isPaid ? 'border-green-500 bg-green-500 shadow-[0_0_8px_rgba(34,197,94,0.4)]' : 'border-white/40'
                }`}>
                  {formData.isPaid && <div className="w-2 h-2 bg-white rounded-full" />}
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-white">Paid Agent</div>
                  <div className="text-xs text-white/60">
                    Set a price and earn money
                  </div>
                </div>
              </div>
            </div>

            {/* Price Input for Paid Option */}
            {formData.isPaid && (
              <div className="space-y-2">
                <Label htmlFor="publish-price" className="text-xs font-medium text-white/60">
                  Price (USD)
                </Label>
                <div className="relative w-28">
                  <DollarSign className="absolute left-2.5 top-1/2 transform -translate-y-1/2 w-3.5 h-3.5 text-white/40" />
                  <Input
                    id="publish-price"
                    type="number"
                    value={formData.price}
                    onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
                    placeholder="0.00"
                    min="1"
                    max="1000"
                    step="0.01"
                    className="pl-8 pr-3 bg-gradient-to-br from-white/[0.04] to-white/[0.02] border border-white/[0.12] text-white placeholder:text-white/40 focus:border-white/[0.2] focus:bg-white/[0.06] focus:outline-none focus:ring-0 h-8 text-sm rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] transition-all duration-200 w-full"
                    required={formData.isPaid}
                  />
                </div>
                <p className="text-xs text-white/50">
                  Range: $1 - $1,000
                </p>
              </div>
            )}
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end gap-2 pt-3 border-t border-white/[0.06]">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              className="bg-gradient-to-br from-white/[0.04] to-white/[0.02] border border-white/[0.12] text-white/70 hover:text-white hover:bg-white/[0.06] hover:border-white/[0.2] h-9 px-4 text-sm font-medium rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] transition-all duration-200"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading || !formData.agentId || !formData.name || !formData.category || (formData.isPaid && !formData.price)}
              className="bg-gradient-to-br from-white to-gray-100 hover:from-gray-100 hover:to-white text-black h-9 px-4 text-sm font-medium rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.3),0_4px_12px_rgba(255,255,255,0.2)] hover:shadow-[inset_0_1px_0_rgba(255,255,255,0.4),0_6px_16px_rgba(255,255,255,0.3)] transition-all duration-200"
            >
              {loading ? 'Publishing...' : formData.isPaid ? 'Publish & List for Sale' : 'Publish as Free'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default PublishAgentModal;
