import React, { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useWhopUser } from '@/contexts/WhopContext';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Globe, ArrowLeft } from 'lucide-react';

interface WhopDiscoverGuardProps {
  children: React.ReactNode;
}

/**
 * Route guard that prevents Whop users from accessing the discover/marketplace page.
 * Regular web users can access it normally, but Whop users are redirected to home.
 */
const WhopDiscoverGuard: React.FC<WhopDiscoverGuardProps> = ({ children }) => {
  const { isWhopUser, isLoading } = useWhopUser();

  // Show loading state while checking user type
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-[#0A0A0A]">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white/30"></div>
      </div>
    );
  }

  // If user is a Whop user, redirect them to home
  if (isWhopUser) {
    return <Navigate to="/home" replace />;
  }

  // For regular web users, render the discover page normally
  return <>{children}</>;
};

export default WhopDiscoverGuard;
