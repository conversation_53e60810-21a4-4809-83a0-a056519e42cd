import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Shield, CreditCard, CheckCircle, AlertCircle } from 'lucide-react';
import { iframeSdk } from '@/lib/iframe-sdk';

interface AccessPassPurchaseProps {
  onPurchaseComplete?: () => void;
  onPurchaseError?: (error: string) => void;
}

const AccessPassPurchase: React.FC<AccessPassPurchaseProps> = ({
  onPurchaseComplete,
  onPurchaseError
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handlePurchase = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      console.log('🛒 Starting Osis access pass purchase...');
      
      // Use the specific access pass ID for Osis
      const accessPassId = 'plan_OPaOlKrrVNZre';
      
      const result = await iframeSdk.inAppPurchase({
        planId: accessPassId
      });

      console.log('💳 Purchase result:', result);

      if (result.status === 'ok') {
        setSuccess(true);
        console.log('✅ Purchase successful!', result.data);
        
        // Call success callback
        if (onPurchaseComplete) {
          onPurchaseComplete();
        }
        
        // Refresh the page after a short delay to update access status
        setTimeout(() => {
          window.location.reload();
        }, 2000);
        
      } else {
        const errorMessage = result.error || 'Purchase failed';
        setError(errorMessage);
        console.error('❌ Purchase failed:', errorMessage);
        
        if (onPurchaseError) {
          onPurchaseError(errorMessage);
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Purchase failed';
      setError(errorMessage);
      console.error('❌ Purchase error:', error);
      
      if (onPurchaseError) {
        onPurchaseError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <Card className="p-8 bg-green-900/20 border-green-800 text-center">
        <div className="flex flex-col items-center space-y-4">
          <CheckCircle className="h-12 w-12 text-green-500" />
          <h2 className="text-xl font-semibold text-green-400">Purchase Successful!</h2>
          <p className="text-green-300">
            You now have access to Osis. The page will refresh shortly...
          </p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-8 bg-gray-900/50 border-gray-800 text-center">
      <div className="flex flex-col items-center space-y-6">
        <Shield className="h-16 w-16 text-blue-500" />
        
        <div className="space-y-2">
          <h2 className="text-2xl font-bold text-white">Access Required</h2>
          <p className="text-gray-400 max-w-md">
            To use Osis, you need to purchase the access pass. This gives you full access to all trading tools and features.
          </p>
        </div>

        {error && (
          <div className="flex items-center space-x-2 text-red-400 bg-red-900/20 p-3 rounded-lg border border-red-800">
            <AlertCircle className="h-5 w-5" />
            <span className="text-sm">{error}</span>
          </div>
        )}

        <Button
          onClick={handlePurchase}
          disabled={isLoading}
          className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <CreditCard className="mr-2 h-5 w-5" />
              Purchase Access Pass
            </>
          )}
        </Button>

        <p className="text-xs text-gray-500 max-w-sm">
          Secure payment processed by Whop. You'll be redirected to complete your purchase.
        </p>
      </div>
    </Card>
  );
};

export default AccessPassPurchase;
