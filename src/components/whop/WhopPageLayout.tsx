import React, { useEffect } from 'react';
import Who<PERSON>BottomNavigation from './WhopBottomNavigation';
import WhopPaymentModal from './WhopPaymentModal';
import { useWhopUser } from '@/contexts/WhopContext';
import { useWhopPayment } from '@/contexts/WhopPaymentContext';

interface WhopPageLayoutProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Layout wrapper for Whop users that provides consistent mobile-style navigation
 * across all pages. This component should be used to wrap page content for Whop users.
 * Includes hard paywall functionality that blocks access until payment is completed.
 */
const WhopPageLayout: React.FC<WhopPageLayoutProps> = ({ children, className = '' }) => {
  const { isWhopUser, user: whopUser } = useWhopUser();
  const { hasPaid, showPaymentModal, setPaymentSuccess, setPaymentError, setShowPaymentModal } = useWhopPayment();

  // Calculate access based on payment state
  const hasAccess = !isWhopUser || hasPaid;

  // Show payment modal for Whop users who haven't paid
  useEffect(() => {
    if (isWhopUser && whopUser && !hasPaid) {
      setShowPaymentModal(true);
    } else {
      setShowPaymentModal(false);
    }
  }, [isWhopUser, whopUser, hasPaid, setShowPaymentModal]);

  // If not a Whop user, just render children without the layout
  if (!isWhopUser) {
    return <>{children}</>;
  }

  const handlePaymentSuccess = () => {
    console.log('🎉 Payment successful in WhopPageLayout');
    // Set payment success in context with a dummy receipt ID
    // In a real implementation, this would come from the payment result
    setPaymentSuccess('whop_payment_' + Date.now());
  };

  const handlePaymentError = (error: string) => {
    console.error('❌ Payment error in WhopPageLayout:', error);
    setPaymentError(error);
  };

  return (
    <div className={`h-screen bg-[#0A0A0A] relative overflow-hidden ${className}`}>
      {/* Payment Modal - Hard paywall that blocks all functionality */}
      <WhopPaymentModal
        isOpen={showPaymentModal}
        onPaymentSuccess={handlePaymentSuccess}
        onPaymentError={handlePaymentError}
      />

      {/* Main content - Only accessible after payment */}
      {hasAccess ? (
        <>
          {/* Scrollable main content area with bottom padding to prevent overlap with navigation */}
          <div className="h-full overflow-auto pb-20 whop-scrollable">
            {children}
          </div>

          {/* Whop-specific bottom navigation - Fixed at bottom */}
          <WhopBottomNavigation />
        </>
      ) : (
        /* Blocked content overlay - prevents any interaction while payment modal is open */
        <div className="h-full flex items-center justify-center bg-[#0A0A0A]">
          <div className="text-center space-y-4 p-8">
            <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-400"></div>
            </div>
            <p className="text-white/60 text-sm">
              Please complete payment to access Osis
            </p>
          </div>
        </div>
      )}

      {/* Custom scrollbar styles for Whop layout */}
      <style>{`
        .whop-scrollable::-webkit-scrollbar {
          width: 6px;
        }

        .whop-scrollable::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.02);
        }

        .whop-scrollable::-webkit-scrollbar-thumb {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 3px;
        }

        .whop-scrollable::-webkit-scrollbar-thumb:hover {
          background: rgba(255, 255, 255, 0.15);
        }
      `}</style>
    </div>
  );
};

export default WhopPageLayout;
