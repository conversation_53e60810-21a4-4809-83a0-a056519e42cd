/* Chart Leaderboard Styles - Osis Design System */

.chart-leaderboard-container {
  position: absolute;
  z-index: 1000;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
}

/* Compact View */
.chart-leaderboard-compact {
  background: linear-gradient(145deg, #141414 0%, #0F0F0F 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 12px;
  min-width: 140px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.05),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2),
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 4px 16px rgba(0, 0, 0, 0.2);
}

.chart-leaderboard-compact:hover {
  background: linear-gradient(145deg, #1A1A1A 0%, #141414 100%);
  border-color: rgba(255, 255, 255, 0.12);
  transform: translateY(-2px);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.08),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3),
    0 12px 40px rgba(0, 0, 0, 0.5),
    0 6px 20px rgba(0, 0, 0, 0.3);
}

.compact-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
}

.compact-title {
  font-size: 12px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  flex: 1;
}

.compact-leader {
  display: flex;
  align-items: center;
  gap: 8px;
}

.leader-rank {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.leader-info {
  flex: 1;
}

.leader-name {
  font-size: 11px;
  font-weight: 600;
  color: white;
  line-height: 1.2;
}

.leader-return {
  font-size: 10px;
  font-weight: 500;
  color: #00e7b6;
  line-height: 1.2;
}

/* Expanded View */
.chart-leaderboard-expanded {
  background: linear-gradient(145deg, #141414 0%, #0F0F0F 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  padding: 16px;
  min-width: 280px;
  max-width: 320px;
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.05),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2),
    0 16px 48px rgba(0, 0, 0, 0.5),
    0 8px 24px rgba(0, 0, 0, 0.3);
}

.expanded-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.expanded-title {
  font-size: 14px;
  font-weight: 600;
  color: white;
}

.collapse-button {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  padding: 6px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collapse-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.9);
}

/* Leaderboard List */
.leaderboard-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.leaderboard-entry {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 12px;
  background: #1A1A1A;
  border: 1px solid rgba(255, 255, 255, 0.04);
  border-radius: 10px;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.02),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.leaderboard-entry:hover {
  background: rgba(26, 26, 26, 0.8);
  border-color: rgba(255, 255, 255, 0.08);
  transform: translateX(2px);
}

.leaderboard-entry:first-child {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.08) 0%, rgba(255, 193, 7, 0.04) 100%);
  border-color: rgba(255, 215, 0, 0.15);
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.1);
}

.leaderboard-entry:first-child::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.3), transparent);
}

.entry-rank {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.entry-info {
  flex: 1;
  min-width: 0;
}

.entry-name {
  font-size: 13px;
  font-weight: 600;
  color: white;
  line-height: 1.2;
  margin-bottom: 2px;
}

.entry-value {
  font-size: 11px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.2;
}

.entry-return {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.return-value {
  font-size: 12px;
  font-weight: 600;
  line-height: 1;
}

.return-value.positive {
  color: #00e7b6;
  text-shadow: 0 0 8px rgba(0, 231, 182, 0.3);
}

.return-value.negative {
  color: #ff4757;
  text-shadow: 0 0 8px rgba(255, 71, 87, 0.3);
}

.return-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.return-icon .positive {
  color: #00e7b6;
}

.return-icon .negative {
  color: #ff4757;
}

/* Loading State */
.leaderboard-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-top: 2px solid rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .chart-leaderboard-expanded {
    min-width: 260px;
    max-width: 280px;
  }
  
  .entry-name {
    font-size: 12px;
  }
  
  .entry-value {
    font-size: 10px;
  }
  
  .return-value {
    font-size: 11px;
  }
}

/* Animation improvements */
.chart-leaderboard-container * {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Glassmorphism enhancement */
.chart-leaderboard-compact,
.chart-leaderboard-expanded {
  position: relative;
}

.chart-leaderboard-compact::before,
.chart-leaderboard-expanded::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
  border-radius: inherit;
  pointer-events: none;
}
