import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { WhopPaymentProvider, useWhopPayment } from '@/contexts/WhopPaymentContext';
import WhopPaymentModal from '@/components/whop/WhopPaymentModal';
import { WhopProvider } from '@/contexts/WhopContext';

// Mock the Whop React SDK
vi.mock('@whop/react', () => ({
  useIframeSdk: () => ({
    inAppPurchase: vi.fn().mockResolvedValue({
      status: 'ok',
      data: { receipt_id: 'test_receipt_123' }
    })
  })
}));

// Mock the Whop context
const mockWhopContext = {
  isWhopUser: true,
  whopUser: {
    id: 'test_user_123',
    username: 'testuser',
    email: '<EMAIL>'
  },
  accessResult: null,
  experienceId: 'test_experience',
  isLoading: false,
  error: null,
  hasSupabaseSession: false,
  refreshWhopAuth: vi.fn(),
  clearWhopAuth: vi.fn()
};

vi.mock('@/contexts/WhopContext', () => ({
  WhopProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useWhopUser: () => mockWhopContext
}));

// Test component to access payment context
const TestComponent = () => {
  const { showPaymentModal, hasPaid } = useWhopPayment();
  return (
    <div>
      <div data-testid="show-modal">{showPaymentModal.toString()}</div>
      <div data-testid="has-paid">{hasPaid.toString()}</div>
    </div>
  );
};

describe('WhopPaymentModal', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
    vi.clearAllMocks();
  });

  it('renders payment modal when user has not paid', () => {
    const mockOnSuccess = vi.fn();
    const mockOnError = vi.fn();

    render(
      <WhopProvider>
        <WhopPaymentProvider>
          <WhopPaymentModal
            isOpen={true}
            onPaymentSuccess={mockOnSuccess}
            onPaymentError={mockOnError}
          />
        </WhopPaymentProvider>
      </WhopProvider>
    );

    expect(screen.getByText('Unlock Osis')).toBeInTheDocument();
    expect(screen.getByText('$15')).toBeInTheDocument();
    expect(screen.getByText('Pay $15 & Get Access')).toBeInTheDocument();
  });

  it('shows features list in the modal', () => {
    const mockOnSuccess = vi.fn();
    const mockOnError = vi.fn();

    render(
      <WhopProvider>
        <WhopPaymentProvider>
          <WhopPaymentModal
            isOpen={true}
            onPaymentSuccess={mockOnSuccess}
            onPaymentError={mockOnError}
          />
        </WhopPaymentProvider>
      </WhopProvider>
    );

    expect(screen.getByText('AI-powered trading agents')).toBeInTheDocument();
    expect(screen.getByText('Advanced stock scanner')).toBeInTheDocument();
    expect(screen.getByText('Real-time market analysis')).toBeInTheDocument();
    expect(screen.getByText('Custom agent builder')).toBeInTheDocument();
    expect(screen.getByText('Portfolio management')).toBeInTheDocument();
    expect(screen.getByText('Community marketplace')).toBeInTheDocument();
  });

  it('handles payment success correctly', async () => {
    const mockOnSuccess = vi.fn();
    const mockOnError = vi.fn();

    render(
      <WhopProvider>
        <WhopPaymentProvider>
          <WhopPaymentModal
            isOpen={true}
            onPaymentSuccess={mockOnSuccess}
            onPaymentError={mockOnError}
          />
        </WhopPaymentProvider>
      </WhopProvider>
    );

    const payButton = screen.getByText('Pay $15 & Get Access');
    fireEvent.click(payButton);

    // Should show loading state
    expect(screen.getByText('Processing Payment...')).toBeInTheDocument();

    // Wait for success state
    await waitFor(() => {
      expect(screen.getByText('Payment Successful!')).toBeInTheDocument();
    });

    // Should call success callback after delay
    await waitFor(() => {
      expect(mockOnSuccess).toHaveBeenCalled();
    }, { timeout: 3000 });
  });

  it('shows payment modal for Whop users who have not paid', () => {
    render(
      <WhopProvider>
        <WhopPaymentProvider>
          <TestComponent />
        </WhopPaymentProvider>
      </WhopProvider>
    );

    expect(screen.getByTestId('show-modal')).toHaveTextContent('true');
    expect(screen.getByTestId('has-paid')).toHaveTextContent('false');
  });

  it('persists payment state in localStorage', () => {
    // Mock localStorage with existing payment
    const paymentState = {
      hasPaid: true,
      receiptId: 'test_receipt_123',
      timestamp: new Date().toISOString()
    };
    localStorage.setItem('whop_payment_state', JSON.stringify(paymentState));

    render(
      <WhopProvider>
        <WhopPaymentProvider>
          <TestComponent />
        </WhopPaymentProvider>
      </WhopProvider>
    );

    expect(screen.getByTestId('show-modal')).toHaveTextContent('false');
    expect(screen.getByTestId('has-paid')).toHaveTextContent('true');
  });

  it('does not show modal when modal is closed', () => {
    const mockOnSuccess = vi.fn();
    const mockOnError = vi.fn();

    render(
      <WhopProvider>
        <WhopPaymentProvider>
          <WhopPaymentModal
            isOpen={false}
            onPaymentSuccess={mockOnSuccess}
            onPaymentError={mockOnError}
          />
        </WhopPaymentProvider>
      </WhopProvider>
    );

    expect(screen.queryByText('Unlock Osis')).not.toBeInTheDocument();
  });

  it('shows retry button on payment error', async () => {
    // Mock payment failure
    const mockIframeSdk = {
      inAppPurchase: vi.fn().mockResolvedValue({
        status: 'error',
        error: 'Payment failed'
      })
    };

    vi.mocked(require('@whop/react').useIframeSdk).mockReturnValue(mockIframeSdk);

    const mockOnSuccess = vi.fn();
    const mockOnError = vi.fn();

    render(
      <WhopProvider>
        <WhopPaymentProvider>
          <WhopPaymentModal
            isOpen={true}
            onPaymentSuccess={mockOnSuccess}
            onPaymentError={mockOnError}
          />
        </WhopPaymentProvider>
      </WhopProvider>
    );

    const payButton = screen.getByText('Pay $15 & Get Access');
    fireEvent.click(payButton);

    await waitFor(() => {
      expect(screen.getByText('Payment failed. Please try again.')).toBeInTheDocument();
      expect(screen.getByText('Try Again')).toBeInTheDocument();
    });

    expect(mockOnError).toHaveBeenCalledWith('Payment failed');
  });
});
