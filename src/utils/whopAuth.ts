import { whopApiClient, isWhopContext } from '@/lib/whop-sdk';

export interface WhopUser {
  id: string;
  username: string;
  email?: string;
  profilePicUrl?: string;
  isWhopUser: true;
}

export interface WhopAccessResult {
  hasAccess: boolean;
  accessLevel: 'admin' | 'customer' | 'no_access';
  userId?: string;
  accessPassId?: string;
}

/**
 * Get current Whop user (server extracts token from headers)
 */
export const getCurrentWhopUser = async (): Promise<WhopUser | null> => {
  try {
    console.log('🔐 Getting current Whop user...');

    // Call server-side API to get current user (server extracts token from headers)
    const response = await whopApiClient.getCurrentUser();

    if (!response.success || !response.user) {
      console.warn('❌ Failed to get current Whop user');
      return null;
    }

    const user = response.user;
    console.log('✅ Whop user retrieved:', { userId: user.id, username: user.username });

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      profilePicUrl: user.profilePicUrl,
      isWhopUser: true
    };
  } catch (error) {
    console.error('❌ Error getting current Whop user:', error);
    return null;
  }
};

/**
 * Legacy function for backward compatibility
 */
export const verifyWhopUserToken = async (_token?: string): Promise<WhopUser | null> => {
  console.warn('⚠️ verifyWhopUserToken is deprecated, use getCurrentWhopUser() instead');
  return getCurrentWhopUser();
};

/**
 * Check if user has access to the Osis access pass
 */
export const checkWhopAccess = async (
  userId: string
): Promise<WhopAccessResult> => {
  try {
    const accessPassId = 'plan_OPaOlKrrVNZre';
    console.log('🔍 Checking Whop access pass for user:', { userId, accessPassId });

    // Call server-side API to check access pass
    const response = await whopApiClient.checkAccess(userId);

    console.log('📋 Whop API response:', {
      success: response.success,
      hasAccess: response.access?.hasAccess,
      accessLevel: response.access?.accessLevel,
      error: response.error
    });

    if (!response.success || !response.access) {
      console.warn('❌ Failed to check Whop access pass:', {
        responseSuccess: response.success,
        hasAccessData: !!response.access,
        error: response.error
      });
      return {
        hasAccess: false,
        accessLevel: 'no_access',
        userId,
        accessPassId
      };
    }

    const result = response.access;
    console.log('✅ Whop access pass check result:', {
      hasAccess: result.hasAccess,
      accessLevel: result.accessLevel,
      userId,
      accessPassId
    });

    return {
      hasAccess: result.hasAccess,
      accessLevel: result.accessLevel,
      userId,
      accessPassId
    };
  } catch (error) {
    console.error('❌ Error checking Whop access pass:', {
      error: error.message,
      userId
    });
    return {
      hasAccess: false,
      accessLevel: 'no_access',
      userId,
      accessPassId: 'plan_OPaOlKrrVNZre'
    };
  }
};

/**
 * Check if we're in a Whop iframe context
 * Note: We don't extract tokens client-side anymore - server handles that
 */
export const isInWhopIframeContext = (): boolean => {
  if (typeof window === 'undefined') {
    return false;
  }

  // Check if we're in an iframe
  const isInIframe = window !== window.parent;

  // Check for Whop-specific indicators
  let isWhopContext = false;
  let parentHostname = 'unknown';

  try {
    // Try to access parent hostname (will fail in cross-origin iframe)
    parentHostname = window.parent?.location?.hostname || 'unknown';
    isWhopContext = parentHostname === 'whop.com';
  } catch (error) {
    // Cross-origin error means we're in an iframe from a different domain
    // This is expected for Whop iframes
    console.log('🔒 Cross-origin iframe detected (expected for Whop)');

    // Check other indicators
    isWhopContext = document.referrer.includes('whop.com') ||
                   window.location.pathname.startsWith('/experiences/') ||
                   new URLSearchParams(window.location.search).has('whop') ||
                   window.location.hostname === 'localhost' && window.location.port === '3001'; // Whop proxy
  }

  const result = isInIframe && isWhopContext;

  console.log('🔍 Whop iframe context check:', {
    isInIframe,
    isWhopContext,
    parentHostname,
    referrer: document.referrer,
    hasExperiencePath: window.location.pathname.startsWith('/experiences/'),
    hasWhopParam: new URLSearchParams(window.location.search).has('whop'),
    result
  });

  return result;
};

/**
 * Legacy function - tokens are now handled server-side
 */
export const getWhopUserTokenFromContext = (): string | null => {
  console.warn('⚠️ getWhopUserTokenFromContext is deprecated - tokens are handled server-side');
  return null;
};

/**
 * Store Whop user token for development purposes
 */
export const storeWhopUserToken = (token: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('whop_user_token', token);
    console.log('💾 Stored Whop user token for development');
  }
};

/**
 * Clear stored Whop user token
 */
export const clearWhopUserToken = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('whop_user_token');
    console.log('🗑️ Cleared Whop user token');
  }
};

/**
 * Check if current context is a Whop environment
 */
export const isInWhopEnvironment = (): boolean => {
  if (typeof window === 'undefined') return false;

  // Use the proper iframe detection
  const isInWhopIframe = isInWhopIframeContext();

  // Check URL-based indicators
  const urlIndicators = isWhopContext();

  const result = isInWhopIframe || urlIndicators;

  console.log('🔍 Whop environment check:', {
    isInWhopIframe,
    urlIndicators,
    result
  });

  return result;
};

/**
 * Get experience ID from current URL
 */
export const getExperienceIdFromUrl = (): string | null => {
  if (typeof window === 'undefined') {
    return null;
  }

  const pathMatch = window.location.pathname.match(/\/experiences\/([^\/]+)/);
  return pathMatch ? pathMatch[1] : null;
};

/**
 * Initialize Whop authentication context
 */
export const initializeWhopAuth = async (): Promise<{
  isWhopUser: boolean;
  user: WhopUser | null;
  accessResult: WhopAccessResult | null;
}> => {
  console.log('🚀 Initializing Whop authentication...');

  // Check if we're in a Whop iframe context
  if (!isInWhopIframeContext() && !isInWhopEnvironment()) {
    console.log('ℹ️ Not in Whop environment, skipping Whop auth');
    return {
      isWhopUser: false,
      user: null,
      accessResult: null
    };
  }

  // Get current user from server (server extracts token from headers)
  const user = await getCurrentWhopUser();
  if (!user) {
    console.log('ℹ️ No Whop user found');
    return {
      isWhopUser: false,
      user: null,
      accessResult: null
    };
  }

  // Check access to Osis access pass
  const experienceId = getExperienceIdFromUrl();
  let accessResult: WhopAccessResult | null = null;

  console.log('🔍 Experience ID from URL:', experienceId);
  console.log('🔍 Checking access to Osis access pass for user:', user.id);

  // Always check access pass regardless of experience ID
  accessResult = await checkWhopAccess(user.id);

  console.log('✅ Whop authentication initialized:', {
    user: user.username,
    userId: user.id,
    experienceId,
    accessPassId: accessResult?.accessPassId,
    companyId: import.meta.env.VITE_WHOP_COMPANY_ID,
    hasAccess: accessResult?.hasAccess,
    accessLevel: accessResult?.accessLevel,
    accessResult
  });

  return {
    isWhopUser: true,
    user,
    accessResult
  };
};
