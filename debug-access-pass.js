// Debug script to test access pass functionality
// Run this in the browser console while on a Whop experience page

async function debugAccessPass() {
  console.log('🔍 Starting Access Pass Debug Test...');
  
  // Test 1: Check if we can call the access pass API directly
  try {
    console.log('📡 Testing direct API call to /api/whop/check-access...');
    
    const response = await fetch('/api/whop/check-access', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        userId: 'user_test123' // Test user ID
      })
    });
    
    const result = await response.json();
    console.log('📋 Direct API Response:', result);
    
    if (result.success && result.access) {
      console.log('✅ API is working! Access pass ID:', result.access.accessPassId);
      if (result.access.accessPassId === 'plan_OPaOlKrrVNZre') {
        console.log('✅ Correct access pass ID detected!');
      } else {
        console.log('❌ Wrong access pass ID:', result.access.accessPassId);
      }
    } else {
      console.log('❌ API call failed:', result.error);
    }
  } catch (error) {
    console.error('❌ API call error:', error);
  }
  
  // Test 2: Check current Whop context
  console.log('\n🔍 Checking current Whop context...');
  
  const whopAuthState = localStorage.getItem('whop_auth_state');
  const whopUserData = localStorage.getItem('whop_user_data');
  const whopAccessResult = localStorage.getItem('whop_access_result');
  
  if (whopAuthState) {
    try {
      const authState = JSON.parse(whopAuthState);
      console.log('📂 Whop Auth State:', authState);
    } catch (e) {
      console.log('❌ Could not parse auth state');
    }
  }
  
  if (whopUserData) {
    try {
      const userData = JSON.parse(whopUserData);
      console.log('👤 Whop User Data:', userData);
    } catch (e) {
      console.log('❌ Could not parse user data');
    }
  }
  
  if (whopAccessResult) {
    try {
      const accessResult = JSON.parse(whopAccessResult);
      console.log('🔑 Whop Access Result:', accessResult);
      
      if (accessResult.accessPassId) {
        console.log('✅ Access pass ID found:', accessResult.accessPassId);
        if (accessResult.accessPassId === 'plan_OPaOlKrrVNZre') {
          console.log('✅ Correct access pass ID in storage!');
        } else {
          console.log('❌ Wrong access pass ID in storage:', accessResult.accessPassId);
        }
      } else if (accessResult.experienceId) {
        console.log('⚠️ Old experience-based access detected:', accessResult.experienceId);
        console.log('❌ System is still using old logic!');
      }
    } catch (e) {
      console.log('❌ Could not parse access result');
    }
  }
  
  // Test 3: Check if access pass purchase component would show
  console.log('\n🔍 Testing access pass purchase logic...');
  
  // Simulate no access scenario
  try {
    const testUserId = 'user_no_access_test';
    const testResponse = await fetch('/api/whop/check-access', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ userId: testUserId })
    });
    
    const testResult = await testResponse.json();
    console.log('📋 No-access test result:', testResult);
    
    if (testResult.success && !testResult.access.hasAccess) {
      console.log('✅ No-access scenario works - purchase component should show');
    }
  } catch (error) {
    console.log('❌ No-access test failed:', error);
  }
  
  console.log('\n🎯 Debug test complete!');
  console.log('📝 Summary:');
  console.log('- Check the logs above for ✅ or ❌ indicators');
  console.log('- Look for "plan_OPaOlKrrVNZre" access pass ID');
  console.log('- If you see "experienceId" instead of "accessPassId", the old system is still running');
}

// Run the debug test
debugAccessPass();
