import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import { WhopServerSdk } from "npm:@whop/api@latest"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-whop-user-token',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS'
};

// Handle OPTIONS requests for CORS
function handleOptions() {
  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
}

// Initialize Supabase client
const supabase = createClient(
  Deno.env.get('SUPABASE_URL') || '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
);

// Initialize Whop SDK
const whopSdk = WhopServerSdk({
  appId: Deno.env.get('WHOP_APP_ID') || '',
  appApiKey: Deno.env.get('WHOP_API_KEY') || '',
  onBehalfOfUserId: Deno.env.get('WHOP_AGENT_USER_ID') || '',
  companyId: Deno.env.get('WHOP_COMPANY_ID') || ''
});

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return handleOptions();
  }

  try {
    const url = new URL(req.url);
    const action = url.searchParams.get('action') || 'verify-user';

    console.log(`🔧 Whop Integration - Action: ${action}`);

    switch (action) {
      case 'verify-user':
        return await handleVerifyUser(req);
      
      case 'check-access':
        return await handleCheckAccess(req);
      
      case 'webhook':
        return await handleWebhook(req);
      
      case 'test-connection':
        return await handleTestConnection(req);
      
      default:
        return new Response(
          JSON.stringify({ error: 'Invalid action' }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        );
    }
  } catch (error) {
    console.error('❌ Whop Integration Error:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});

async function handleVerifyUser(req: Request) {
  try {
    // Get user token from headers
    const userToken = req.headers.get('x-whop-user-token');
    
    if (!userToken) {
      return new Response(
        JSON.stringify({ error: 'No user token provided' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    console.log('🔐 Verifying Whop user token...');
    console.log('Full user token:', userToken);

    // Verify the token using Whop SDK
    const { userId } = await whopSdk.verifyUserToken(userToken);

    if (!userId) {
      return new Response(
        JSON.stringify({ error: 'Invalid user token' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Get user information
    const user = await whopSdk.users.getUser({ userId });

    if (!user) {
      return new Response(
        JSON.stringify({ error: 'User not found' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    console.log('✅ Whop user verified:', { userId: user.id, username: user.username });

    return new Response(
      JSON.stringify({
        success: true,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          profilePicUrl: user.profilePicUrl
        }
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  } catch (error) {
    console.error('❌ Error verifying user:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Failed to verify user',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
}

async function handleCheckAccess(req: Request) {
  try {
    const { userId } = await req.json();

    if (!userId) {
      return new Response(
        JSON.stringify({ error: 'Missing userId' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Check access to the specific Osis access pass
    const accessPassId = 'plan_OPaOlKrrVNZre';
    console.log('🔍 Checking Whop access pass:', { userId, accessPassId });

    const result = await whopSdk.access.checkIfUserHasAccessToAccessPass({
      userId,
      accessPassId,
    });

    console.log('✅ Access pass check result:', result);

    return new Response(
      JSON.stringify({
        success: true,
        access: {
          hasAccess: result.hasAccess,
          accessLevel: result.accessLevel,
          userId,
          accessPassId
        }
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  } catch (error) {
    console.error('❌ Error checking access:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Failed to check access',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
}

async function handleWebhook(req: Request) {
  try {
    const payload = await req.json();
    
    console.log('🔔 Whop webhook received:', payload);

    // Handle different webhook events
    const { event, data } = payload;

    switch (event) {
      case 'user.access_granted':
        console.log('✅ User access granted:', data);
        // You could update your database here
        break;

      case 'user.access_revoked':
        console.log('❌ User access revoked:', data);
        // You could update your database here
        break;

      default:
        console.log('ℹ️ Unhandled webhook event:', event);
    }

    return new Response(
      JSON.stringify({ success: true, event, processed: true }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  } catch (error) {
    console.error('❌ Error handling webhook:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Failed to handle webhook',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
}

async function handleTestConnection(req: Request) {
  try {
    console.log('🧪 Testing Whop connection...');

    // Try to get agent user information
    const agentUserId = Deno.env.get('WHOP_AGENT_USER_ID');
    
    if (!agentUserId) {
      return new Response(
        JSON.stringify({ error: 'No agent user ID configured' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const user = await whopSdk.users.getUser({ userId: agentUserId });

    if (!user) {
      return new Response(
        JSON.stringify({ error: 'Could not fetch agent user' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    console.log('✅ Whop connection test successful');

    return new Response(
      JSON.stringify({
        success: true,
        connected: true,
        agentUser: {
          id: user.id,
          username: user.username
        },
        config: {
          appId: Deno.env.get('WHOP_APP_ID'),
          hasApiKey: !!Deno.env.get('WHOP_API_KEY'),
          agentUserId: Deno.env.get('WHOP_AGENT_USER_ID'),
          companyId: Deno.env.get('WHOP_COMPANY_ID')
        }
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  } catch (error) {
    console.error('❌ Whop connection test failed:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Connection test failed',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
}
