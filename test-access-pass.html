<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Access Pass API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #0A0A0A;
            color: white;
        }
        .test-section {
            background: #1a1a1a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #333;
        }
        button {
            background: #0066cc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0052a3;
        }
        .result {
            background: #2a2a2a;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-left: 4px solid #00aa00;
        }
        .error {
            border-left: 4px solid #aa0000;
        }
        input {
            background: #333;
            color: white;
            border: 1px solid #555;
            padding: 8px;
            border-radius: 4px;
            margin: 5px;
            width: 200px;
        }
    </style>
</head>
<body>
    <h1>🔐 Access Pass API Test</h1>
    <p>This page tests the new access pass verification system for Osis.</p>

    <div class="test-section">
        <h2>Test Access Pass Check</h2>
        <p>Test the access pass verification API endpoint.</p>
        
        <div>
            <label>User ID:</label>
            <input type="text" id="userId" placeholder="user_XXXXXXXX" value="user_test123">
        </div>
        
        <button onclick="testAccessPass()">Test Access Pass</button>
        <div id="accessResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test Environment</h2>
        <button onclick="testEnvironment()">Check Environment</button>
        <div id="envResult" class="result"></div>
    </div>

    <script>
        async function testAccessPass() {
            const userId = document.getElementById('userId').value;
            const resultDiv = document.getElementById('accessResult');
            
            if (!userId) {
                resultDiv.textContent = 'Please enter a User ID';
                resultDiv.className = 'result error';
                return;
            }

            try {
                resultDiv.textContent = 'Testing access pass...';
                resultDiv.className = 'result';

                const response = await fetch('/api/whop/check-access', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ userId })
                });

                const data = await response.json();
                
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = data.success ? 'result success' : 'result error';

                // Log details
                console.log('Access Pass Test Result:', data);
                
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
                resultDiv.className = 'result error';
                console.error('Access pass test error:', error);
            }
        }

        async function testEnvironment() {
            const resultDiv = document.getElementById('envResult');
            
            try {
                resultDiv.textContent = 'Checking environment...';
                resultDiv.className = 'result';

                const response = await fetch('/api/whop/env-debug');
                const data = await response.json();
                
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = 'result success';
                
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
                resultDiv.className = 'result error';
                console.error('Environment test error:', error);
            }
        }

        // Auto-run environment check on load
        window.addEventListener('load', () => {
            testEnvironment();
        });
    </script>
</body>
</html>
